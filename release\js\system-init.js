/**
 * 系统初始化脚本
 * 负责协调各个模块的初始化和配置
 */
class SystemInitializer {
    constructor() {
        this.initSteps = [
            { name: '配置管理器', fn: this.initConfigManager.bind(this) },
            { name: '日志系统', fn: this.initLogger.bind(this) },
            { name: '资源管理器', fn: this.initResourceManager.bind(this) },
            { name: '安全工具', fn: this.initSecurityUtils.bind(this) },
            { name: '错误处理', fn: this.initErrorHandling.bind(this) },
            { name: '性能监控', fn: this.initPerformanceMonitoring.bind(this) },
            { name: '用户界面', fn: this.initUI.bind(this) },
            { name: '智能讲解系统', fn: this.initPresentationSystem.bind(this) }
        ];
        
        this.initialized = false;
        this.initProgress = 0;
        this.errors = [];
    }
    
    /**
     * 开始系统初始化
     */
    async initialize() {
        console.log('🚀 开始系统初始化...');
        
        try {
            // 显示初始化进度
            this.showInitProgress();
            
            // 逐步执行初始化
            for (let i = 0; i < this.initSteps.length; i++) {
                const step = this.initSteps[i];
                
                try {
                    console.log(`📋 正在初始化: ${step.name}`);
                    this.updateProgress(i, step.name);
                    
                    await step.fn();
                    
                    console.log(`✅ ${step.name} 初始化完成`);
                } catch (error) {
                    console.error(`❌ ${step.name} 初始化失败:`, error);
                    this.errors.push({ step: step.name, error: error });
                    
                    // 记录到日志系统（如果已初始化）
                    if (window.logger) {
                        window.logger.error(`${step.name}初始化失败`, {
                            error: error.message,
                            stack: error.stack
                        }, 'system-init');
                    }
                }
                
                this.initProgress = ((i + 1) / this.initSteps.length) * 100;
            }
            
            // 完成初始化
            this.initialized = true;
            this.hideInitProgress();
            
            console.log('🎉 系统初始化完成!');
            
            // 记录初始化结果
            if (window.logger) {
                window.logger.info('系统初始化完成', {
                    totalSteps: this.initSteps.length,
                    errors: this.errors.length,
                    success: this.errors.length === 0
                }, 'system-init');
            }
            
            // 触发初始化完成事件
            this.dispatchInitCompleteEvent();
            
            // 如果有错误，显示错误报告
            if (this.errors.length > 0) {
                this.showErrorReport();
            }
            
        } catch (error) {
            console.error('💥 系统初始化失败:', error);
            this.hideInitProgress();
            this.showFatalError(error);
        }
    }
    
    /**
     * 初始化配置管理器
     */
    async initConfigManager() {
        if (!window.ConfigManager || !window.appConfig) {
            throw new Error('配置管理器未加载');
        }
        
        // 设置开发环境配置
        if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
            window.appConfig.set('system.environment', 'development');
            window.appConfig.set('system.debug', true);
        }
        
        // 监听配置变更
        window.appConfig.onChange('system', (newValue) => {
            console.log('系统配置已更新:', newValue);
        });
    }
    
    /**
     * 初始化日志系统
     */
    async initLogger() {
        if (!window.Logger || !window.logger) {
            throw new Error('日志系统未加载');
        }
        
        // 设置日志级别
        const debugMode = window.appConfig?.get('system.debug', false);
        if (debugMode) {
            window.logger.setLevel('DEBUG');
        }
        
        // 记录系统启动
        window.logger.info('智能讲解系统启动', {
            version: window.appConfig?.get('system.version', '2.0.0'),
            environment: window.appConfig?.get('system.environment', 'production'),
            userAgent: navigator.userAgent,
            url: location.href
        }, 'system-init');
    }
    
    /**
     * 初始化资源管理器
     */
    async initResourceManager() {
        if (!window.ResourceManager || !window.resourceManager) {
            throw new Error('资源管理器未加载');
        }
        
        // 添加系统清理任务
        window.resourceManager.addCleanupTask(() => {
            console.log('执行系统清理任务');
            
            // 清理全局变量
            if (window.presentationController) {
                window.presentationController.cleanup?.();
            }
            
            // 清理语音引擎
            if (window.speechEngine) {
                window.speechEngine.cleanup?.();
            }
        });
    }
    
    /**
     * 初始化安全工具
     */
    async initSecurityUtils() {
        if (!window.SecurityUtils || !window.securityUtils) {
            throw new Error('安全工具未加载');
        }
        
        // 添加当前域名到白名单
        const currentDomain = location.hostname;
        window.securityUtils.addAllowedDomain(currentDomain);
        
        // 如果是开发环境，添加常用开发域名
        if (window.appConfig?.get('system.environment') === 'development') {
            window.securityUtils.addAllowedDomain('localhost');
            window.securityUtils.addAllowedDomain('127.0.0.1');
        }
        
        // 执行安全检查
        const securityCheck = window.securityUtils.performSecurityCheck();
        
        if (window.logger) {
            window.logger.info('安全检查完成', securityCheck, 'security');
        }
    }
    
    /**
     * 初始化错误处理
     */
    async initErrorHandling() {
        // 设置全局错误处理策略
        window.addEventListener('error', (event) => {
            if (window.logger) {
                window.logger.error('全局JavaScript错误', {
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error?.stack
                }, 'global-error');
            }
        });
        
        // 设置Promise拒绝处理
        window.addEventListener('unhandledrejection', (event) => {
            if (window.logger) {
                window.logger.error('未处理的Promise拒绝', {
                    reason: event.reason,
                    stack: event.reason?.stack
                }, 'promise-rejection');
            }
        });
    }
    
    /**
     * 初始化性能监控
     */
    async initPerformanceMonitoring() {
        // 记录页面加载性能
        if (performance.timing) {
            const timing = performance.timing;
            const loadTime = timing.loadEventEnd - timing.navigationStart;
            const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
            
            if (window.logger) {
                window.logger.info('页面性能指标', {
                    loadTime: `${loadTime}ms`,
                    domReady: `${domReady}ms`,
                    dns: `${timing.domainLookupEnd - timing.domainLookupStart}ms`,
                    connect: `${timing.connectEnd - timing.connectStart}ms`,
                    response: `${timing.responseEnd - timing.responseStart}ms`
                }, 'performance');
            }
        }
        
        // 监控内存使用
        if (performance.memory) {
            const memoryInfo = {
                used: `${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
                total: `${(performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
                limit: `${(performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`
            };
            
            if (window.logger) {
                window.logger.info('内存使用情况', memoryInfo, 'performance');
            }
        }
    }
    
    /**
     * 初始化用户界面
     */
    async initUI() {
        // 设置主题
        const theme = window.appConfig?.get('ui.theme', 'default');
        document.body.setAttribute('data-theme', theme);
        
        // 设置语言
        const language = window.appConfig?.get('ui.language', 'zh-CN');
        document.documentElement.lang = language;
        
        // 初始化快捷键
        this.initKeyboardShortcuts();
        
        // 初始化工具提示
        this.initTooltips();
    }
    
    /**
     * 初始化智能讲解系统
     */
    async initPresentationSystem() {
        // 等待智能讲解系统加载
        await this.waitForPresentationSystem();
        
        // 初始化语音引擎
        if (window.speechEngine) {
            const voiceConfig = {
                voice: window.appConfig?.get('speech.defaultVoice', 'Microsoft Yaoyao'),
                rate: window.appConfig?.get('speech.rate', 0.8),
                pitch: window.appConfig?.get('speech.pitch', 1.0),
                volume: window.appConfig?.get('speech.volume', 0.8)
            };
            
            window.speechEngine.updateConfig(voiceConfig);
        }
        
        // 初始化演示控制器
        if (window.presentationController) {
            window.presentationController.init?.();
        }
    }
    
    /**
     * 等待智能讲解系统加载
     */
    async waitForPresentationSystem() {
        return new Promise((resolve) => {
            const checkInterval = setInterval(() => {
                if (window.speechEngine && window.presentationController) {
                    clearInterval(checkInterval);
                    resolve();
                }
            }, 100);
            
            // 超时处理
            setTimeout(() => {
                clearInterval(checkInterval);
                resolve();
            }, 5000);
        });
    }
    
    /**
     * 初始化键盘快捷键
     */
    initKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+Shift+P: 启动演示
            if (event.ctrlKey && event.shiftKey && event.key === 'P') {
                event.preventDefault();
                if (window.startPresentation) {
                    window.startPresentation();
                }
            }
            
            // Ctrl+Shift+S: 停止演示
            if (event.ctrlKey && event.shiftKey && event.key === 'S') {
                event.preventDefault();
                if (window.stopPresentation) {
                    window.stopPresentation();
                }
            }
            
            // Ctrl+Shift+D: 切换调试模式
            if (event.ctrlKey && event.shiftKey && event.key === 'D') {
                event.preventDefault();
                const currentDebug = window.appConfig?.get('system.debug', false);
                window.appConfig?.set('system.debug', !currentDebug);
                
                if (window.logger) {
                    window.logger.info('调试模式切换', { 
                        debug: !currentDebug 
                    }, 'system-init');
                }
            }
        });
    }
    
    /**
     * 初始化工具提示
     */
    initTooltips() {
        // 为所有带有title属性的元素添加增强的工具提示
        document.addEventListener('mouseover', (event) => {
            const element = event.target;
            if (element.title && !element.hasAttribute('data-tooltip-enhanced')) {
                element.setAttribute('data-tooltip-enhanced', 'true');
                // 这里可以添加更丰富的工具提示功能
            }
        });
    }
    
    /**
     * 显示初始化进度
     */
    showInitProgress() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            const textElement = overlay.querySelector('.loading-text div:first-child');
            if (textElement) {
                textElement.textContent = '正在初始化系统...';
            }
            overlay.style.display = 'flex';
        }
    }
    
    /**
     * 更新初始化进度
     */
    updateProgress(stepIndex, stepName) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            const textElement = overlay.querySelector('.loading-text div:first-child');
            if (textElement) {
                textElement.textContent = `正在初始化: ${stepName}`;
            }
            
            const subTextElement = overlay.querySelector('.loading-text div:last-child');
            if (subTextElement) {
                const progress = Math.round(((stepIndex + 1) / this.initSteps.length) * 100);
                subTextElement.textContent = `进度: ${progress}% (${stepIndex + 1}/${this.initSteps.length})`;
            }
        }
    }
    
    /**
     * 隐藏初始化进度
     */
    hideInitProgress() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
    
    /**
     * 显示错误报告
     */
    showErrorReport() {
        const errorCount = this.errors.length;
        const message = `系统初始化完成，但有 ${errorCount} 个模块初始化失败。\n\n` +
                       this.errors.map(e => `• ${e.step}: ${e.error.message}`).join('\n') +
                       '\n\n系统可能无法正常工作，建议刷新页面重试。';
        
        console.warn('系统初始化错误报告:', this.errors);
        
        // 可以在这里显示更友好的错误界面
        if (confirm(message + '\n\n是否要查看详细错误信息？')) {
            console.table(this.errors);
        }
    }
    
    /**
     * 显示致命错误
     */
    showFatalError(error) {
        const message = `系统初始化失败: ${error.message}\n\n请刷新页面重试，如果问题持续存在，请联系技术支持。`;
        
        console.error('致命错误:', error);
        alert(message);
    }
    
    /**
     * 触发初始化完成事件
     */
    dispatchInitCompleteEvent() {
        const event = new CustomEvent('systemInitialized', {
            detail: {
                success: this.errors.length === 0,
                errors: this.errors,
                initTime: Date.now()
            }
        });
        
        window.dispatchEvent(event);
    }
    
    /**
     * 获取初始化状态
     */
    getStatus() {
        return {
            initialized: this.initialized,
            progress: this.initProgress,
            errors: this.errors,
            totalSteps: this.initSteps.length
        };
    }
}

// 创建全局初始化器实例
window.SystemInitializer = SystemInitializer;
window.systemInitializer = new SystemInitializer();

// 页面加载完成后自动初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.systemInitializer.initialize();
    });
} else {
    // 如果页面已经加载完成，立即初始化
    window.systemInitializer.initialize();
}

// 暴露到全局作用域
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SystemInitializer;
}
