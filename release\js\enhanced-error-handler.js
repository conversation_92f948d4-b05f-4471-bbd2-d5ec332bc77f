/**
 * 增强的错误处理系统
 * 提供统一的错误处理、重试机制和用户友好的错误提示
 */
class EnhancedErrorHandler {
    constructor() {
        // 错误类型定义
        this.errorTypes = {
            NETWORK: 'network',
            SPEECH: 'speech',
            EXTENSION: 'extension',
            SCRIPT: 'script',
            VALIDATION: 'validation',
            PERMISSION: 'permission',
            TIMEOUT: 'timeout',
            UNKNOWN: 'unknown'
        };
        
        // 错误严重级别
        this.severityLevels = {
            LOW: 'low',
            MEDIUM: 'medium',
            HIGH: 'high',
            CRITICAL: 'critical'
        };
        
        // 重试策略配置
        this.retryConfig = {
            maxRetries: 3,
            baseDelay: 1000,
            maxDelay: 10000,
            backoffMultiplier: 2
        };
        
        // 错误统计
        this.errorStats = {
            total: 0,
            byType: {},
            bySeverity: {},
            resolved: 0,
            unresolved: 0
        };
        
        // 错误处理策略
        this.strategies = new Map();
        
        // 用户通知配置
        this.notificationConfig = {
            showToUser: true,
            autoHide: true,
            hideDelay: 5000
        };
        
        this.init();
    }
    
    /**
     * 初始化错误处理系统
     */
    init() {
        // 注册默认错误处理策略
        this.registerDefaultStrategies();
        
        // 设置全局错误监听
        this.setupGlobalErrorHandling();
        
        if (window.logger) {
            window.logger.info('增强错误处理系统已初始化', {
                strategies: this.strategies.size,
                retryConfig: this.retryConfig
            }, 'error-handler');
        }
    }
    
    /**
     * 注册默认错误处理策略
     */
    registerDefaultStrategies() {
        // 网络错误策略
        this.registerStrategy(this.errorTypes.NETWORK, {
            severity: this.severityLevels.MEDIUM,
            retryable: true,
            userMessage: '网络连接出现问题，正在重试...',
            handler: this.handleNetworkError.bind(this)
        });
        
        // 语音错误策略
        this.registerStrategy(this.errorTypes.SPEECH, {
            severity: this.severityLevels.LOW,
            retryable: true,
            userMessage: '语音播放失败，尝试切换到静默模式',
            handler: this.handleSpeechError.bind(this)
        });
        
        // 扩展错误策略
        this.registerStrategy(this.errorTypes.EXTENSION, {
            severity: this.severityLevels.HIGH,
            retryable: false,
            userMessage: '浏览器扩展未安装或已禁用，部分功能可能无法使用',
            handler: this.handleExtensionError.bind(this)
        });
        
        // 脚本错误策略
        this.registerStrategy(this.errorTypes.SCRIPT, {
            severity: this.severityLevels.HIGH,
            retryable: false,
            userMessage: '脚本执行出错，请检查脚本内容',
            handler: this.handleScriptError.bind(this)
        });
        
        // 验证错误策略
        this.registerStrategy(this.errorTypes.VALIDATION, {
            severity: this.severityLevels.MEDIUM,
            retryable: false,
            userMessage: '输入数据验证失败，请检查输入内容',
            handler: this.handleValidationError.bind(this)
        });
        
        // 权限错误策略
        this.registerStrategy(this.errorTypes.PERMISSION, {
            severity: this.severityLevels.HIGH,
            retryable: false,
            userMessage: '权限不足，请检查浏览器设置',
            handler: this.handlePermissionError.bind(this)
        });
        
        // 超时错误策略
        this.registerStrategy(this.errorTypes.TIMEOUT, {
            severity: this.severityLevels.MEDIUM,
            retryable: true,
            userMessage: '操作超时，正在重试...',
            handler: this.handleTimeoutError.bind(this)
        });
        
        // 未知错误策略
        this.registerStrategy(this.errorTypes.UNKNOWN, {
            severity: this.severityLevels.MEDIUM,
            retryable: false,
            userMessage: '发生未知错误，请稍后重试',
            handler: this.handleUnknownError.bind(this)
        });
    }
    
    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // JavaScript错误
        window.addEventListener('error', (event) => {
            this.handleError({
                type: this.errorTypes.SCRIPT,
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                originalEvent: event
            });
        });
        
        // Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: this.errorTypes.SCRIPT,
                message: 'Unhandled Promise Rejection',
                reason: event.reason,
                stack: event.reason?.stack,
                originalEvent: event
            });
        });
    }
    
    /**
     * 注册错误处理策略
     * @param {string} errorType - 错误类型
     * @param {object} strategy - 处理策略
     */
    registerStrategy(errorType, strategy) {
        this.strategies.set(errorType, {
            severity: strategy.severity || this.severityLevels.MEDIUM,
            retryable: strategy.retryable || false,
            userMessage: strategy.userMessage || '发生错误',
            handler: strategy.handler || this.handleUnknownError.bind(this),
            maxRetries: strategy.maxRetries || this.retryConfig.maxRetries
        });
    }
    
    /**
     * 处理错误
     * @param {object} error - 错误对象
     * @param {object} context - 上下文信息
     * @returns {Promise} 处理结果
     */
    async handleError(error, context = {}) {
        // 标准化错误对象
        const standardError = this.standardizeError(error);
        
        // 更新统计信息
        this.updateStats(standardError);
        
        // 记录错误
        this.logError(standardError, context);
        
        // 获取处理策略
        const strategy = this.strategies.get(standardError.type) || 
                        this.strategies.get(this.errorTypes.UNKNOWN);
        
        try {
            // 执行错误处理策略
            const result = await this.executeStrategy(standardError, strategy, context);
            
            // 如果处理成功，更新统计
            if (result.resolved) {
                this.errorStats.resolved++;
            } else {
                this.errorStats.unresolved++;
            }
            
            return result;
            
        } catch (handlerError) {
            // 处理器本身出错
            if (window.logger) {
                window.logger.error('错误处理器执行失败', {
                    originalError: standardError,
                    handlerError: handlerError.message
                }, 'error-handler');
            }
            
            this.errorStats.unresolved++;
            return { resolved: false, error: handlerError };
        }
    }
    
    /**
     * 执行错误处理策略
     * @param {object} error - 标准化错误对象
     * @param {object} strategy - 处理策略
     * @param {object} context - 上下文信息
     * @returns {Promise} 处理结果
     */
    async executeStrategy(error, strategy, context) {
        // 显示用户通知
        if (this.notificationConfig.showToUser) {
            this.showUserNotification(error, strategy);
        }
        
        // 如果错误可重试，执行重试逻辑
        if (strategy.retryable && context.retryCount < strategy.maxRetries) {
            return await this.retryWithBackoff(error, strategy, context);
        }
        
        // 执行具体的错误处理逻辑
        const result = await strategy.handler(error, context);
        
        return {
            resolved: result.resolved || false,
            message: result.message || strategy.userMessage,
            action: result.action || 'none',
            data: result.data || null
        };
    }
    
    /**
     * 带退避的重试机制
     * @param {object} error - 错误对象
     * @param {object} strategy - 处理策略
     * @param {object} context - 上下文信息
     * @returns {Promise} 重试结果
     */
    async retryWithBackoff(error, strategy, context) {
        const retryCount = (context.retryCount || 0) + 1;
        const delay = Math.min(
            this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, retryCount - 1),
            this.retryConfig.maxDelay
        );
        
        if (window.logger) {
            window.logger.info('开始重试', {
                errorType: error.type,
                retryCount: retryCount,
                delay: delay,
                maxRetries: strategy.maxRetries
            }, 'error-handler');
        }
        
        // 等待延迟
        await new Promise(resolve => setTimeout(resolve, delay));
        
        try {
            // 执行重试逻辑
            if (context.retryFunction) {
                const retryResult = await context.retryFunction();
                
                if (window.logger) {
                    window.logger.info('重试成功', {
                        errorType: error.type,
                        retryCount: retryCount
                    }, 'error-handler');
                }
                
                return {
                    resolved: true,
                    message: '重试成功',
                    action: 'retry_success',
                    data: retryResult
                };
            }
        } catch (retryError) {
            // 重试失败，递归处理
            const newContext = { ...context, retryCount: retryCount };
            return await this.handleError(retryError, newContext);
        }
        
        return {
            resolved: false,
            message: `重试 ${retryCount} 次后仍然失败`,
            action: 'retry_failed',
            data: null
        };
    }
    
    /**
     * 标准化错误对象
     * @param {*} error - 原始错误
     * @returns {object} 标准化错误对象
     */
    standardizeError(error) {
        if (typeof error === 'string') {
            return {
                type: this.errorTypes.UNKNOWN,
                message: error,
                timestamp: Date.now(),
                id: this.generateErrorId()
            };
        }
        
        if (error instanceof Error) {
            return {
                type: this.classifyError(error),
                message: error.message,
                stack: error.stack,
                name: error.name,
                timestamp: Date.now(),
                id: this.generateErrorId()
            };
        }
        
        return {
            type: error.type || this.errorTypes.UNKNOWN,
            message: error.message || '未知错误',
            stack: error.stack || null,
            timestamp: error.timestamp || Date.now(),
            id: error.id || this.generateErrorId(),
            ...error
        };
    }
    
    /**
     * 分类错误类型
     * @param {Error} error - 错误对象
     * @returns {string} 错误类型
     */
    classifyError(error) {
        const message = error.message.toLowerCase();
        
        if (message.includes('network') || message.includes('fetch')) {
            return this.errorTypes.NETWORK;
        }
        
        if (message.includes('speech') || message.includes('voice')) {
            return this.errorTypes.SPEECH;
        }
        
        if (message.includes('extension') || message.includes('chrome')) {
            return this.errorTypes.EXTENSION;
        }
        
        if (message.includes('timeout')) {
            return this.errorTypes.TIMEOUT;
        }
        
        if (message.includes('permission') || message.includes('denied')) {
            return this.errorTypes.PERMISSION;
        }
        
        if (message.includes('validation') || message.includes('invalid')) {
            return this.errorTypes.VALIDATION;
        }
        
        return this.errorTypes.SCRIPT;
    }
    
    /**
     * 更新错误统计
     * @param {object} error - 错误对象
     */
    updateStats(error) {
        this.errorStats.total++;
        
        // 按类型统计
        this.errorStats.byType[error.type] = (this.errorStats.byType[error.type] || 0) + 1;
        
        // 按严重级别统计
        const strategy = this.strategies.get(error.type);
        if (strategy) {
            this.errorStats.bySeverity[strategy.severity] = 
                (this.errorStats.bySeverity[strategy.severity] || 0) + 1;
        }
    }
    
    /**
     * 记录错误日志
     * @param {object} error - 错误对象
     * @param {object} context - 上下文信息
     */
    logError(error, context) {
        if (window.logger) {
            const strategy = this.strategies.get(error.type);
            
            window.logger.error('错误处理', {
                errorId: error.id,
                type: error.type,
                message: error.message,
                severity: strategy?.severity || 'unknown',
                retryable: strategy?.retryable || false,
                context: context,
                stack: error.stack
            }, 'error-handler');
        }
    }
    
    /**
     * 显示用户通知
     * @param {object} error - 错误对象
     * @param {object} strategy - 处理策略
     */
    showUserNotification(error, strategy) {
        // 这里可以集成更复杂的通知系统
        if (strategy.severity === this.severityLevels.CRITICAL) {
            alert(strategy.userMessage);
        } else if (strategy.severity === this.severityLevels.HIGH) {
            console.warn(strategy.userMessage);
            // 可以显示toast通知
        } else {
            console.info(strategy.userMessage);
        }
    }
    
    /**
     * 网络错误处理器
     */
    async handleNetworkError(error, context) {
        return {
            resolved: false,
            message: '网络连接失败，请检查网络设置',
            action: 'check_network'
        };
    }
    
    /**
     * 语音错误处理器
     */
    async handleSpeechError(error, context) {
        // 尝试切换到静默模式
        if (window.appConfig) {
            window.appConfig.set('speech.enabled', false);
        }
        
        return {
            resolved: true,
            message: '已切换到静默模式',
            action: 'fallback_to_silent'
        };
    }
    
    /**
     * 扩展错误处理器
     */
    async handleExtensionError(error, context) {
        return {
            resolved: false,
            message: '请安装或启用浏览器扩展',
            action: 'install_extension'
        };
    }
    
    /**
     * 脚本错误处理器
     */
    async handleScriptError(error, context) {
        return {
            resolved: false,
            message: '脚本执行失败，请检查脚本语法',
            action: 'check_script'
        };
    }
    
    /**
     * 验证错误处理器
     */
    async handleValidationError(error, context) {
        return {
            resolved: false,
            message: '数据验证失败，请检查输入',
            action: 'validate_input'
        };
    }
    
    /**
     * 权限错误处理器
     */
    async handlePermissionError(error, context) {
        return {
            resolved: false,
            message: '权限不足，请检查浏览器设置',
            action: 'check_permissions'
        };
    }
    
    /**
     * 超时错误处理器
     */
    async handleTimeoutError(error, context) {
        return {
            resolved: false,
            message: '操作超时，请稍后重试',
            action: 'retry_later'
        };
    }
    
    /**
     * 未知错误处理器
     */
    async handleUnknownError(error, context) {
        return {
            resolved: false,
            message: '发生未知错误，请联系技术支持',
            action: 'contact_support'
        };
    }
    
    /**
     * 生成错误ID
     * @returns {string} 错误ID
     */
    generateErrorId() {
        return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 获取错误统计信息
     * @returns {object} 统计信息
     */
    getStats() {
        return {
            ...this.errorStats,
            strategies: this.strategies.size,
            successRate: this.errorStats.total > 0 ? 
                (this.errorStats.resolved / this.errorStats.total * 100).toFixed(2) + '%' : '0%'
        };
    }
    
    /**
     * 清除错误统计
     */
    clearStats() {
        this.errorStats = {
            total: 0,
            byType: {},
            bySeverity: {},
            resolved: 0,
            unresolved: 0
        };
        
        if (window.logger) {
            window.logger.info('错误统计已清除', {}, 'error-handler');
        }
    }
    
    /**
     * 导出错误报告
     * @returns {object} 错误报告
     */
    exportReport() {
        return {
            timestamp: new Date().toISOString(),
            stats: this.getStats(),
            strategies: Array.from(this.strategies.entries()).map(([type, strategy]) => ({
                type: type,
                severity: strategy.severity,
                retryable: strategy.retryable,
                userMessage: strategy.userMessage
            })),
            config: {
                retryConfig: this.retryConfig,
                notificationConfig: this.notificationConfig
            }
        };
    }
}

// 创建全局增强错误处理器实例
window.EnhancedErrorHandler = EnhancedErrorHandler;
window.enhancedErrorHandler = new EnhancedErrorHandler();

// 暴露到全局作用域
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedErrorHandler;
}
