<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能讲解系统 - 通用版</title>
    
    <!-- 通用版专用样式 -->
    <link rel="stylesheet" href="css/intelligent-presentation.css">
    
    <!-- 录制面板样式 -->
    <link rel="stylesheet" href="css/recorder-panel.css">

    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="../lib/fontawesome/css/all.min.css">
        

</head>
<body>
    <!-- 系统容器 -->
    <div class="system-container">
        <!-- 加载遮罩 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">
                <div>正在加载目标系统...</div>
                <div style="font-size: 12px; margin-top: 10px; color: #999;">
                    智能讲解系统准备中
                </div>
            </div>
        </div>
        
        <!-- 目标系统iframe -->
        <iframe id="targetSystem" class="system-iframe" src="about:blank"></iframe>
    </div>
    
    <!-- 配置切换按钮 -->
    <button class="config-toggle" id="configToggle" title="配置系统">
        <i class="fas fa-cog"></i>
    </button>
    
    <!-- 配置面板 -->
    <div class="config-panel" id="configPanel">
        <h3 style="margin-top: 0; color: #333;">
            <i class="fas fa-cog"></i> 系统配置
        </h3>
        
        <div class="form-group">
            <label for="targetUrl">目标系统URL:</label>
            <input type="url" id="targetUrl" placeholder="https://agmpuat.yunfeiyun.com/" 
                   value="https://agmpuat.yunfeiyun.com/">
        </div>
        
        <div class="form-group">
            <label for="systemTitle">系统标题:</label>
            <input type="text" id="systemTitle" placeholder="系统名称" 
                   value="智能讲解演示系统">
        </div>
        
        <button class="btn-load" onclick="loadTargetSystem()">
            <i class="fas fa-play"></i> 加载系统
        </button>

        <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;">
            <h4 style="margin: 0 0 10px 0; color: #333; font-size: 14px;">
                <i class="fas fa-rocket"></i> 快速启动
            </h4>
            <div style="display: flex; gap: 5px; margin-bottom: 10px;">
                <button style="flex: 1; padding: 6px 8px; font-size: 12px; background: #17a2b8; color: white; border: none; border-radius: 3px; cursor: pointer;"
                        onclick="quickStart('basic')" title="基础系统介绍">
                    基础
                </button>
                <button style="flex: 1; padding: 6px 8px; font-size: 12px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;"
                        onclick="quickStart('stats')" title="系统统计信息">
                    统计
                </button>
                <button style="flex: 1; padding: 6px 8px; font-size: 12px; background: #ffc107; color: #212529; border: none; border-radius: 3px; cursor: pointer;"
                        onclick="quickStart('example')" title="示例讲解">
                    示例
                </button>
            </div>
            <div style="display: flex; gap: 5px;">
                <button style="flex: 1; padding: 6px 8px; font-size: 12px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer;"
                        onclick="analyzePage()" title="分析页面元素">
                    分析
                </button>
                <button style="flex: 1; padding: 6px 8px; font-size: 12px; background: #6f42c1; color: white; border: none; border-radius: 3px; cursor: pointer;"
                        onclick="suggestSelectors()" title="获取推荐选择器">
                    选择器
                </button>
            </div>
        </div>

        <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #eee;">
            <small style="color: #666;">
                <i class="fas fa-info-circle"></i>
                快捷键: Ctrl+Shift+P 启动讲解
            </small>
        </div>
    </div>
    
    <!-- 扩展插件提醒 -->
    <div class="extension-notice" id="extensionNotice">
        <h4><i class="fas fa-puzzle-piece"></i> 需要安装浏览器扩展</h4>
        <p>为了获得最佳的智能讲解体验，建议安装我们的浏览器扩展插件。扩展插件可以提供更强大的页面交互功能。</p>
        <div class="btn-group">
            <button class="btn-install" onclick="installExtension()">
                <i class="fas fa-download"></i> 安装扩展
            </button>
            <button class="btn-dismiss" onclick="dismissNotice()">
                <i class="fas fa-times"></i> 暂不安装
            </button>
        </div>
    </div>
    
    <!-- 智能讲解UI组件 -->
    <div id="presentation-ui"></div>
    
    <!-- 核心系统模块 - 按依赖顺序加载 -->
    <script src="js/config-manager.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/resource-manager.js"></script>
    <script src="js/security-utils.js"></script>
    <script src="js/enhanced-error-handler.js"></script>
    <script src="js/performance-monitor.js"></script>
    <script src="js/system-status-panel.js"></script>
    <script src="js/system-init.js"></script>
    <script src="js/system-init.js"></script>

    <!-- 智能讲解系统 - 按依赖顺序加载 -->
    <script src="../AI/speech-engine.js"></script>
    <script src="../AI/ui-controller.js"></script>
    <script src="../AI/error-handler.js"></script>
    <script src="../AI/presentation-controller.js"></script>
    <script src="../AI/presentation-script.js"></script>
    <script src="../AI/presentation-script-dynamic-text-test.js"></script>
    <script src="../AI/presentation-main.js"></script>

    <!-- 语音控制系统 -->
    <script src="../AI/voice-controller.js"></script>
    <script src="../AI/voice-manager.js"></script>

    <!-- 主页面脚本 -->
    <script src="../js/main-controller.js"></script>
    <script src="../js/iframe-communication.js"></script>

    <!-- 示例脚本和快速启动工具 -->
    <script src="example-script.js"></script>
    <script src="quick-start.js"></script>

    <!-- 通用版主控制脚本 -->
    <script src="js/intelligent-presentation.js"></script>
    
    <!-- 录制面板功能 -->
    <script src="js/modal-manager.js"></script>
    <script src="js/recorder-panel.js"></script>

    <!-- 录制与回放控制面板 -->
    <div id="recorder-panel">
      <div id="recorder-panel-header">
        <span><i class="fas fa-mouse-pointer"></i> 步骤录制与回放</span>
        <div>
          <button id="recorder-toggle-btn" title="折叠/展开"><i class="fas fa-chevron-up"></i></button>
          <button id="recorder-min-btn" title="最小化"><i class="fas fa-window-minimize"></i></button>
        </div>
      </div>
      <div id="recorder-panel-body">
        <div class="control-buttons">
          <button id="recorder-load-btn" class="btn btn-primary">加载页面</button>
          <button id="recorder-start-btn" class="btn btn-success">开始录制</button>
          <button id="recorder-stop-btn" class="btn btn-danger" disabled>停止录制</button>
          <button id="recorder-play-btn" class="btn btn-warning" disabled>回放</button>
        </div>
        <div class="control-buttons">
          <button id="recorder-export-btn" class="btn btn-info" disabled>导出步骤</button>
          <input type="file" id="recorder-import-input" style="display:none;">
          <button id="recorder-import-btn" class="btn btn-secondary">导入步骤</button>
        </div>
        <div id="recorder-status">请先加载页面</div>
        <div class="steps-header">
          <span>已录制步骤</span>
          <span id="recorder-steps-count"></span>
        </div>
        <div id="recorder-steps-list"></div>
      </div>
      <div id="recorder-panel-min">
        <i class="fas fa-mouse-pointer"></i> 步骤录制与回放 <span><i class="fas fa-chevron-up"></i></span>
      </div>
    </div>

    <!-- 编辑弹窗（JSON模式） -->
    <div id="recorder-edit-modal" class="modal-overlay">
      <div id="recorder-edit-dialog" class="modal-dialog">
        <div class="modal-title">编辑步骤（JSON模式）</div>
        <div class="modal-content">
          <textarea id="recorder-edit-json" class="code-textarea"></textarea>
        </div>
        <div class="modal-footer">
          <button type="button" id="edit-cancel-btn" class="btn btn-secondary">取消</button>
          <button type="button" id="edit-save-btn" class="btn btn-primary">保存</button>
        </div>
        <span id="edit-close-btn" class="modal-close">&times;</span>
      </div>
    </div>

    <!-- 导出预览弹窗 -->
    <div id="export-preview-modal" class="modal-overlay">
      <div id="export-preview-dialog" class="modal-dialog">
        <div class="modal-title">导出步骤预览</div>
        <div class="modal-content">
          <div class="export-content">
            <label>步骤脚本内容：</label>
            <textarea id="export-preview-content" class="code-textarea"></textarea>
          </div>
          <div class="export-info">
            <div><strong>说明：</strong></div>
            <div>• 您可以编辑上面的脚本内容，修改后点击下载按钮保存</div>
            <div>• 脚本格式为JavaScript，可直接用于智能讲解系统</div>
            <div>• 共包含 <span id="export-steps-count">0</span> 个步骤</div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" id="export-preview-cancel" class="btn btn-secondary">取消</button>
          <button type="button" id="export-preview-download" class="btn btn-success">下载脚本</button>
        </div>
        <span id="export-preview-close" class="modal-close">&times;</span>
      </div>
    </div>

    <!-- 步骤详情弹窗 -->
    <div id="recorder-detail-modal" class="modal-overlay">
      <div id="recorder-detail-dialog" class="modal-dialog">
        <div class="modal-title">步骤详情</div>
        <div class="modal-content">
          <pre id="recorder-detail-content" class="detail-content"></pre>
        </div>
        <div class="modal-footer">
          <button type="button" id="recorder-detail-close" class="btn btn-primary">关闭</button>
        </div>
        <span id="recorder-detail-x" class="modal-close">&times;</span>
      </div>
    </div>
</body>
</html>
