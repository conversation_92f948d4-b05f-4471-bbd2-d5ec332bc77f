/**
 * 资源管理器
 * 负责管理和清理系统资源，防止内存泄漏
 */
class ResourceManager {
    constructor() {
        // 定时器管理
        this.timers = new Set();
        this.intervals = new Set();
        
        // 事件监听器管理
        this.listeners = new Map();
        
        // DOM观察器管理
        this.observers = new Set();
        
        // 网络请求管理
        this.requests = new Set();
        
        // 资源引用管理
        this.resources = new Map();
        
        // 内存监控
        this.memoryStats = {
            peak: 0,
            current: 0,
            warnings: 0
        };
        
        // 清理任务队列
        this.cleanupTasks = new Set();
        
        // 自动清理配置
        this.autoCleanup = {
            enabled: true,
            interval: 30000, // 30秒
            memoryThreshold: 50 * 1024 * 1024 // 50MB
        };
        
        this.init();
    }
    
    /**
     * 初始化资源管理器
     */
    init() {
        // 监听页面卸载事件
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        
        // 监听页面隐藏事件
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.performMaintenanceCleanup();
            }
        });
        
        // 启动自动清理
        if (this.autoCleanup.enabled) {
            this.startAutoCleanup();
        }
        
        // 监听内存警告
        this.setupMemoryMonitoring();
        
        if (window.logger) {
            window.logger.info('资源管理器已初始化', {
                autoCleanup: this.autoCleanup.enabled,
                memoryThreshold: this.autoCleanup.memoryThreshold
            }, 'resource-manager');
        }
    }
    
    /**
     * 添加定时器
     * @param {number} timer - 定时器ID
     * @param {string} description - 描述
     * @returns {number} 定时器ID
     */
    addTimer(timer, description = '') {
        this.timers.add({
            id: timer,
            description: description,
            createdAt: Date.now(),
            type: 'timeout'
        });
        
        if (window.logger) {
            window.logger.debug('添加定时器', { 
                timerId: timer, 
                description: description 
            }, 'resource-manager');
        }
        
        return timer;
    }
    
    /**
     * 添加间隔定时器
     * @param {number} interval - 间隔定时器ID
     * @param {string} description - 描述
     * @returns {number} 间隔定时器ID
     */
    addInterval(interval, description = '') {
        this.intervals.add({
            id: interval,
            description: description,
            createdAt: Date.now(),
            type: 'interval'
        });
        
        if (window.logger) {
            window.logger.debug('添加间隔定时器', { 
                intervalId: interval, 
                description: description 
            }, 'resource-manager');
        }
        
        return interval;
    }
    
    /**
     * 添加事件监听器
     * @param {Element} element - DOM元素
     * @param {string} event - 事件名称
     * @param {Function} handler - 事件处理器
     * @param {object} options - 选项
     * @returns {string} 监听器ID
     */
    addListener(element, event, handler, options = {}) {
        const listenerId = this.generateId('listener');
        
        // 添加事件监听器
        element.addEventListener(event, handler, options);
        
        // 记录监听器信息
        this.listeners.set(listenerId, {
            element: element,
            event: event,
            handler: handler,
            options: options,
            createdAt: Date.now()
        });
        
        if (window.logger) {
            window.logger.debug('添加事件监听器', { 
                listenerId: listenerId,
                event: event,
                elementTag: element.tagName
            }, 'resource-manager');
        }
        
        return listenerId;
    }
    
    /**
     * 添加DOM观察器
     * @param {MutationObserver|IntersectionObserver|ResizeObserver} observer - 观察器
     * @param {string} description - 描述
     * @returns {string} 观察器ID
     */
    addObserver(observer, description = '') {
        const observerId = this.generateId('observer');
        
        this.observers.add({
            id: observerId,
            observer: observer,
            description: description,
            createdAt: Date.now()
        });
        
        if (window.logger) {
            window.logger.debug('添加DOM观察器', { 
                observerId: observerId,
                description: description,
                type: observer.constructor.name
            }, 'resource-manager');
        }
        
        return observerId;
    }
    
    /**
     * 添加网络请求
     * @param {AbortController} controller - 请求控制器
     * @param {string} url - 请求URL
     * @param {string} method - 请求方法
     * @returns {string} 请求ID
     */
    addRequest(controller, url, method = 'GET') {
        const requestId = this.generateId('request');
        
        this.requests.add({
            id: requestId,
            controller: controller,
            url: url,
            method: method,
            createdAt: Date.now()
        });
        
        if (window.logger) {
            window.logger.debug('添加网络请求', { 
                requestId: requestId,
                url: url,
                method: method
            }, 'resource-manager');
        }
        
        return requestId;
    }
    
    /**
     * 添加资源引用
     * @param {string} key - 资源键
     * @param {*} resource - 资源对象
     * @param {Function} cleanupFn - 清理函数
     */
    addResource(key, resource, cleanupFn = null) {
        this.resources.set(key, {
            resource: resource,
            cleanupFn: cleanupFn,
            createdAt: Date.now()
        });
        
        if (window.logger) {
            window.logger.debug('添加资源引用', { 
                key: key,
                hasCleanupFn: !!cleanupFn
            }, 'resource-manager');
        }
    }
    
    /**
     * 移除定时器
     * @param {number} timer - 定时器ID
     */
    removeTimer(timer) {
        clearTimeout(timer);
        this.timers.forEach(t => {
            if (t.id === timer) {
                this.timers.delete(t);
            }
        });
    }
    
    /**
     * 移除间隔定时器
     * @param {number} interval - 间隔定时器ID
     */
    removeInterval(interval) {
        clearInterval(interval);
        this.intervals.forEach(i => {
            if (i.id === interval) {
                this.intervals.delete(i);
            }
        });
    }
    
    /**
     * 移除事件监听器
     * @param {string} listenerId - 监听器ID
     */
    removeListener(listenerId) {
        const listener = this.listeners.get(listenerId);
        if (listener) {
            listener.element.removeEventListener(
                listener.event, 
                listener.handler, 
                listener.options
            );
            this.listeners.delete(listenerId);
            
            if (window.logger) {
                window.logger.debug('移除事件监听器', { 
                    listenerId: listenerId 
                }, 'resource-manager');
            }
        }
    }
    
    /**
     * 移除观察器
     * @param {string} observerId - 观察器ID
     */
    removeObserver(observerId) {
        this.observers.forEach(obs => {
            if (obs.id === observerId) {
                obs.observer.disconnect();
                this.observers.delete(obs);
                
                if (window.logger) {
                    window.logger.debug('移除DOM观察器', { 
                        observerId: observerId 
                    }, 'resource-manager');
                }
            }
        });
    }
    
    /**
     * 取消网络请求
     * @param {string} requestId - 请求ID
     */
    cancelRequest(requestId) {
        this.requests.forEach(req => {
            if (req.id === requestId) {
                req.controller.abort();
                this.requests.delete(req);
                
                if (window.logger) {
                    window.logger.debug('取消网络请求', { 
                        requestId: requestId,
                        url: req.url
                    }, 'resource-manager');
                }
            }
        });
    }
    
    /**
     * 移除资源引用
     * @param {string} key - 资源键
     */
    removeResource(key) {
        const resource = this.resources.get(key);
        if (resource) {
            if (resource.cleanupFn) {
                try {
                    resource.cleanupFn(resource.resource);
                } catch (error) {
                    if (window.logger) {
                        window.logger.error('资源清理函数执行失败', { 
                            key: key, 
                            error: error.message 
                        }, 'resource-manager');
                    }
                }
            }
            this.resources.delete(key);
        }
    }
    
    /**
     * 执行完整清理
     */
    cleanup() {
        if (window.logger) {
            window.logger.info('开始资源清理', {
                timers: this.timers.size,
                intervals: this.intervals.size,
                listeners: this.listeners.size,
                observers: this.observers.size,
                requests: this.requests.size,
                resources: this.resources.size
            }, 'resource-manager');
        }
        
        // 清理定时器
        this.timers.forEach(timer => {
            clearTimeout(timer.id);
        });
        this.timers.clear();
        
        // 清理间隔定时器
        this.intervals.forEach(interval => {
            clearInterval(interval.id);
        });
        this.intervals.clear();
        
        // 清理事件监听器
        this.listeners.forEach((listener, id) => {
            try {
                listener.element.removeEventListener(
                    listener.event, 
                    listener.handler, 
                    listener.options
                );
            } catch (error) {
                if (window.logger) {
                    window.logger.warn('移除事件监听器失败', { 
                        listenerId: id, 
                        error: error.message 
                    }, 'resource-manager');
                }
            }
        });
        this.listeners.clear();
        
        // 清理观察器
        this.observers.forEach(obs => {
            try {
                obs.observer.disconnect();
            } catch (error) {
                if (window.logger) {
                    window.logger.warn('断开观察器失败', { 
                        observerId: obs.id, 
                        error: error.message 
                    }, 'resource-manager');
                }
            }
        });
        this.observers.clear();
        
        // 取消网络请求
        this.requests.forEach(req => {
            try {
                req.controller.abort();
            } catch (error) {
                // 请求可能已经完成，忽略错误
            }
        });
        this.requests.clear();
        
        // 清理资源引用
        this.resources.forEach((resource, key) => {
            this.removeResource(key);
        });
        
        // 执行自定义清理任务
        this.cleanupTasks.forEach(task => {
            try {
                task();
            } catch (error) {
                if (window.logger) {
                    window.logger.error('清理任务执行失败', { 
                        error: error.message 
                    }, 'resource-manager');
                }
            }
        });
        
        if (window.logger) {
            window.logger.info('资源清理完成', {}, 'resource-manager');
        }
    }
    
    /**
     * 执行维护性清理
     */
    performMaintenanceCleanup() {
        const now = Date.now();
        const maxAge = 5 * 60 * 1000; // 5分钟
        
        // 清理过期的定时器记录
        this.timers.forEach(timer => {
            if (now - timer.createdAt > maxAge) {
                this.timers.delete(timer);
            }
        });
        
        // 清理过期的请求记录
        this.requests.forEach(req => {
            if (now - req.createdAt > maxAge) {
                this.requests.delete(req);
            }
        });
        
        // 检查内存使用情况
        this.checkMemoryUsage();
        
        if (window.logger) {
            window.logger.debug('维护性清理完成', {
                activeTimers: this.timers.size,
                activeListeners: this.listeners.size,
                activeObservers: this.observers.size,
                activeRequests: this.requests.size
            }, 'resource-manager');
        }
    }
    
    /**
     * 添加清理任务
     * @param {Function} task - 清理任务函数
     */
    addCleanupTask(task) {
        this.cleanupTasks.add(task);
    }
    
    /**
     * 移除清理任务
     * @param {Function} task - 清理任务函数
     */
    removeCleanupTask(task) {
        this.cleanupTasks.delete(task);
    }
    
    /**
     * 启动自动清理
     */
    startAutoCleanup() {
        const cleanupInterval = setInterval(() => {
            this.performMaintenanceCleanup();
        }, this.autoCleanup.interval);
        
        this.addInterval(cleanupInterval, 'auto-cleanup');
    }
    
    /**
     * 设置内存监控
     */
    setupMemoryMonitoring() {
        if (performance.memory) {
            const checkMemory = () => {
                const used = performance.memory.usedJSHeapSize;
                this.memoryStats.current = used;
                
                if (used > this.memoryStats.peak) {
                    this.memoryStats.peak = used;
                }
                
                if (used > this.autoCleanup.memoryThreshold) {
                    this.memoryStats.warnings++;
                    
                    if (window.logger) {
                        window.logger.warn('内存使用量过高', {
                            current: `${(used / 1024 / 1024).toFixed(2)}MB`,
                            threshold: `${(this.autoCleanup.memoryThreshold / 1024 / 1024).toFixed(2)}MB`,
                            peak: `${(this.memoryStats.peak / 1024 / 1024).toFixed(2)}MB`
                        }, 'resource-manager');
                    }
                    
                    // 触发紧急清理
                    this.performMaintenanceCleanup();
                }
            };
            
            const memoryCheckInterval = setInterval(checkMemory, 10000); // 每10秒检查一次
            this.addInterval(memoryCheckInterval, 'memory-monitoring');
        }
    }
    
    /**
     * 检查内存使用情况
     */
    checkMemoryUsage() {
        if (performance.memory) {
            const memInfo = {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            };
            
            return {
                ...memInfo,
                usedMB: (memInfo.used / 1024 / 1024).toFixed(2),
                totalMB: (memInfo.total / 1024 / 1024).toFixed(2),
                limitMB: (memInfo.limit / 1024 / 1024).toFixed(2),
                usage: ((memInfo.used / memInfo.limit) * 100).toFixed(2)
            };
        }
        return null;
    }
    
    /**
     * 获取资源统计信息
     * @returns {object} 统计信息
     */
    getStats() {
        return {
            timers: this.timers.size,
            intervals: this.intervals.size,
            listeners: this.listeners.size,
            observers: this.observers.size,
            requests: this.requests.size,
            resources: this.resources.size,
            cleanupTasks: this.cleanupTasks.size,
            memoryStats: this.memoryStats,
            memoryInfo: this.checkMemoryUsage()
        };
    }
    
    /**
     * 生成唯一ID
     * @param {string} prefix - 前缀
     * @returns {string} 唯一ID
     */
    generateId(prefix = 'resource') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

// 创建全局资源管理器实例
window.ResourceManager = ResourceManager;
window.resourceManager = new ResourceManager();

// 暴露到全局作用域
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ResourceManager;
}
