/**
 * 统一配置管理系统
 * 负责管理整个应用的配置参数
 */
class ConfigManager {
    constructor() {
        this.config = {
            // 系统基础配置
            system: {
                version: '2.0.0',
                debug: false,
                environment: 'production',
                name: '智能讲解系统',
                author: 'AI-jiangjie Team'
            },
            
            // 语音配置
            speech: {
                defaultVoice: 'Microsoft Yaoyao',
                rate: 0.8,
                pitch: 1.0,
                volume: 0.8,
                lang: 'zh-CN',
                enabled: true,
                fallbackToSilent: true,
                retryCount: 2
            },
            
            // UI界面配置
            ui: {
                theme: 'default',
                language: 'zh-CN',
                animations: true,
                showProgress: true,
                autoHideControls: false,
                controlsPosition: 'top-left',
                highlightColor: '#00ff88',
                highlightOpacity: 0.3
            },
            
            // 浏览器扩展配置
            extension: {
                timeout: 5000,
                retryCount: 3,
                enabled: true,
                autoDetect: true,
                fallbackMode: true
            },
            
            // 错误处理配置
            errorHandling: {
                maxRetries: 3,
                retryDelay: 1000,
                logErrors: true,
                showUserFriendlyMessages: true,
                autoSkipOnError: true,
                errorLogMaxSize: 100
            },
            
            // 性能配置
            performance: {
                enableCache: true,
                cacheMaxSize: 100,
                cacheTTL: 300000, // 5分钟
                enablePerformanceMonitoring: false,
                maxMemoryUsage: 100 * 1024 * 1024 // 100MB
            },
            
            // 安全配置
            security: {
                validateInputs: true,
                sanitizeHTML: true,
                allowedDomains: [],
                maxScriptExecutionTime: 30000,
                enableCSP: true
            }
        };
        
        // 配置变更监听器
        this.listeners = new Map();
        
        // 从本地存储加载配置
        this.loadFromStorage();
    }
    
    /**
     * 获取配置值
     * @param {string} path - 配置路径，如 'speech.rate' 或 'ui.theme'
     * @param {*} defaultValue - 默认值
     * @returns {*} 配置值
     */
    get(path, defaultValue = undefined) {
        const keys = path.split('.');
        let current = this.config;
        
        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return defaultValue;
            }
        }
        
        return current;
    }
    
    /**
     * 设置配置值
     * @param {string} path - 配置路径
     * @param {*} value - 配置值
     * @param {boolean} persist - 是否持久化到本地存储
     */
    set(path, value, persist = true) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        let current = this.config;
        
        // 导航到目标对象
        for (const key of keys) {
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        
        // 保存旧值用于比较
        const oldValue = current[lastKey];
        
        // 设置新值
        current[lastKey] = value;
        
        // 触发变更事件
        this.notifyListeners(path, value, oldValue);
        
        // 持久化到本地存储
        if (persist) {
            this.saveToStorage();
        }
        
        console.log(`配置已更新: ${path} = ${JSON.stringify(value)}`);
    }
    
    /**
     * 批量设置配置
     * @param {object} configs - 配置对象
     * @param {boolean} persist - 是否持久化
     */
    setMultiple(configs, persist = true) {
        Object.entries(configs).forEach(([path, value]) => {
            this.set(path, value, false);
        });
        
        if (persist) {
            this.saveToStorage();
        }
    }
    
    /**
     * 重置配置到默认值
     * @param {string} path - 配置路径，如果不提供则重置所有配置
     */
    reset(path = null) {
        if (path) {
            // 重置特定配置
            const defaultValue = this.getDefaultValue(path);
            if (defaultValue !== undefined) {
                this.set(path, defaultValue);
            }
        } else {
            // 重置所有配置
            this.config = this.getDefaultConfig();
            this.saveToStorage();
            console.log('所有配置已重置为默认值');
        }
    }
    
    /**
     * 验证配置值
     * @param {string} path - 配置路径
     * @param {*} value - 配置值
     * @returns {boolean} 是否有效
     */
    validate(path, value) {
        const validators = {
            'speech.rate': (v) => typeof v === 'number' && v >= 0.1 && v <= 10,
            'speech.pitch': (v) => typeof v === 'number' && v >= 0 && v <= 2,
            'speech.volume': (v) => typeof v === 'number' && v >= 0 && v <= 1,
            'ui.theme': (v) => typeof v === 'string' && ['default', 'dark', 'light'].includes(v),
            'extension.timeout': (v) => typeof v === 'number' && v > 0,
            'errorHandling.maxRetries': (v) => typeof v === 'number' && v >= 0 && v <= 10
        };
        
        const validator = validators[path];
        if (validator) {
            return validator(value);
        }
        
        // 默认验证：非null且非undefined
        return value !== null && value !== undefined;
    }
    
    /**
     * 监听配置变更
     * @param {string} path - 配置路径
     * @param {function} callback - 回调函数
     */
    onChange(path, callback) {
        if (!this.listeners.has(path)) {
            this.listeners.set(path, []);
        }
        this.listeners.get(path).push(callback);
    }
    
    /**
     * 移除配置变更监听器
     * @param {string} path - 配置路径
     * @param {function} callback - 回调函数
     */
    offChange(path, callback) {
        const pathListeners = this.listeners.get(path);
        if (pathListeners) {
            const index = pathListeners.indexOf(callback);
            if (index > -1) {
                pathListeners.splice(index, 1);
            }
        }
    }
    
    /**
     * 通知监听器配置变更
     * @param {string} path - 配置路径
     * @param {*} newValue - 新值
     * @param {*} oldValue - 旧值
     */
    notifyListeners(path, newValue, oldValue) {
        // 通知精确路径的监听器
        const exactListeners = this.listeners.get(path) || [];
        exactListeners.forEach(callback => {
            try {
                callback(newValue, oldValue, path);
            } catch (error) {
                console.error('配置变更监听器执行错误:', error);
            }
        });
        
        // 通知父路径的监听器
        const pathParts = path.split('.');
        for (let i = pathParts.length - 1; i > 0; i--) {
            const parentPath = pathParts.slice(0, i).join('.');
            const parentListeners = this.listeners.get(parentPath) || [];
            parentListeners.forEach(callback => {
                try {
                    callback(this.get(parentPath), undefined, parentPath);
                } catch (error) {
                    console.error('配置变更监听器执行错误:', error);
                }
            });
        }
    }
    
    /**
     * 从本地存储加载配置
     */
    loadFromStorage() {
        try {
            const stored = localStorage.getItem('intelligent-presentation-config');
            if (stored) {
                const storedConfig = JSON.parse(stored);
                this.mergeConfig(storedConfig);
                console.log('配置已从本地存储加载');
            }
        } catch (error) {
            console.warn('从本地存储加载配置失败:', error);
        }
    }
    
    /**
     * 保存配置到本地存储
     */
    saveToStorage() {
        try {
            localStorage.setItem('intelligent-presentation-config', JSON.stringify(this.config));
        } catch (error) {
            console.warn('保存配置到本地存储失败:', error);
        }
    }
    
    /**
     * 合并配置
     * @param {object} newConfig - 新配置
     */
    mergeConfig(newConfig) {
        this.config = this.deepMerge(this.config, newConfig);
    }
    
    /**
     * 深度合并对象
     * @param {object} target - 目标对象
     * @param {object} source - 源对象
     * @returns {object} 合并后的对象
     */
    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                    result[key] = this.deepMerge(target[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    }
    
    /**
     * 获取默认配置
     * @returns {object} 默认配置
     */
    getDefaultConfig() {
        // 返回初始配置的深拷贝
        return JSON.parse(JSON.stringify(this.constructor.prototype.config || this.config));
    }
    
    /**
     * 获取默认值
     * @param {string} path - 配置路径
     * @returns {*} 默认值
     */
    getDefaultValue(path) {
        const defaultConfig = this.getDefaultConfig();
        const keys = path.split('.');
        let current = defaultConfig;
        
        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return undefined;
            }
        }
        
        return current;
    }
    
    /**
     * 导出配置
     * @returns {object} 当前配置
     */
    export() {
        return JSON.parse(JSON.stringify(this.config));
    }
    
    /**
     * 导入配置
     * @param {object} config - 要导入的配置
     * @param {boolean} merge - 是否合并而不是替换
     */
    import(config, merge = true) {
        if (merge) {
            this.mergeConfig(config);
        } else {
            this.config = config;
        }
        this.saveToStorage();
        console.log('配置已导入');
    }
    
    /**
     * 获取配置摘要信息
     * @returns {object} 配置摘要
     */
    getSummary() {
        return {
            version: this.get('system.version'),
            environment: this.get('system.environment'),
            speechEnabled: this.get('speech.enabled'),
            extensionEnabled: this.get('extension.enabled'),
            debugMode: this.get('system.debug'),
            theme: this.get('ui.theme'),
            language: this.get('ui.language')
        };
    }
}

// 创建全局配置管理器实例
window.ConfigManager = ConfigManager;
window.appConfig = new ConfigManager();

// 暴露到全局作用域以便其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ConfigManager;
}
