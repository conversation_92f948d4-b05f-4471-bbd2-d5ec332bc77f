/**
 * 系统状态面板
 * 提供系统运行状态的可视化监控界面
 */
class SystemStatusPanel {
    constructor() {
        this.isVisible = false;
        this.updateInterval = null;
        this.refreshRate = 2000; // 2秒更新一次
        
        // 面板元素
        this.panel = null;
        this.content = null;
        
        // 状态数据
        this.statusData = {
            system: {},
            performance: {},
            errors: {},
            resources: {},
            security: {}
        };
        
        this.init();
    }
    
    /**
     * 初始化状态面板
     */
    init() {
        this.createPanel();
        this.setupEventListeners();
        
        if (window.logger) {
            window.logger.info('系统状态面板已初始化', {}, 'status-panel');
        }
    }
    
    /**
     * 创建状态面板
     */
    createPanel() {
        // 创建面板容器
        this.panel = document.createElement('div');
        this.panel.id = 'system-status-panel';
        this.panel.className = 'system-status-panel hidden';
        
        // 创建面板HTML结构
        this.panel.innerHTML = `
            <div class="status-panel-header">
                <h3><i class="fas fa-chart-line"></i> 系统状态监控</h3>
                <div class="status-panel-controls">
                    <button id="status-refresh-btn" title="刷新数据">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button id="status-export-btn" title="导出报告">
                        <i class="fas fa-download"></i>
                    </button>
                    <button id="status-close-btn" title="关闭面板">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="status-panel-content" id="status-panel-content">
                <div class="status-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>正在加载系统状态...</span>
                </div>
            </div>
        `;
        
        // 添加样式
        this.addStyles();
        
        // 添加到页面
        document.body.appendChild(this.panel);
        
        // 获取内容容器
        this.content = document.getElementById('status-panel-content');
    }
    
    /**
     * 添加面板样式
     */
    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .system-status-panel {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 800px;
                max-width: 90vw;
                height: 600px;
                max-height: 90vh;
                background: white;
                border-radius: 8px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                z-index: 10000;
                display: flex;
                flex-direction: column;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            
            .system-status-panel.hidden {
                display: none;
            }
            
            .status-panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 20px;
                border-bottom: 1px solid #eee;
                background: #f8f9fa;
                border-radius: 8px 8px 0 0;
            }
            
            .status-panel-header h3 {
                margin: 0;
                color: #333;
                font-size: 16px;
            }
            
            .status-panel-controls {
                display: flex;
                gap: 8px;
            }
            
            .status-panel-controls button {
                background: none;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px 8px;
                cursor: pointer;
                color: #666;
                transition: all 0.2s;
            }
            
            .status-panel-controls button:hover {
                background: #e9ecef;
                color: #333;
            }
            
            .status-panel-content {
                flex: 1;
                padding: 20px;
                overflow-y: auto;
            }
            
            .status-loading {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 200px;
                color: #666;
                gap: 10px;
            }
            
            .status-section {
                margin-bottom: 25px;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                overflow: hidden;
            }
            
            .status-section-header {
                background: #f8f9fa;
                padding: 12px 15px;
                border-bottom: 1px solid #e9ecef;
                font-weight: 600;
                color: #495057;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .status-section-content {
                padding: 15px;
            }
            
            .status-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }
            
            .status-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #f1f3f4;
            }
            
            .status-item:last-child {
                border-bottom: none;
            }
            
            .status-label {
                color: #666;
                font-size: 14px;
            }
            
            .status-value {
                font-weight: 500;
                color: #333;
            }
            
            .status-value.good {
                color: #28a745;
            }
            
            .status-value.warning {
                color: #ffc107;
            }
            
            .status-value.error {
                color: #dc3545;
            }
            
            .status-chart {
                height: 100px;
                background: #f8f9fa;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #666;
                margin-top: 10px;
            }
            
            .status-list {
                max-height: 150px;
                overflow-y: auto;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                background: #f8f9fa;
            }
            
            .status-list-item {
                padding: 8px 12px;
                border-bottom: 1px solid #e9ecef;
                font-size: 13px;
                color: #666;
            }
            
            .status-list-item:last-child {
                border-bottom: none;
            }
            
            .status-badge {
                display: inline-block;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 500;
                text-transform: uppercase;
            }
            
            .status-badge.success {
                background: #d4edda;
                color: #155724;
            }
            
            .status-badge.warning {
                background: #fff3cd;
                color: #856404;
            }
            
            .status-badge.danger {
                background: #f8d7da;
                color: #721c24;
            }
            
            .status-badge.info {
                background: #d1ecf1;
                color: #0c5460;
            }
        `;
        
        document.head.appendChild(style);
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 刷新按钮
        document.addEventListener('click', (event) => {
            if (event.target.closest('#status-refresh-btn')) {
                this.refreshData();
            }
            
            if (event.target.closest('#status-export-btn')) {
                this.exportReport();
            }
            
            if (event.target.closest('#status-close-btn')) {
                this.hide();
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (event) => {
            // Ctrl+Shift+M: 切换状态面板
            if (event.ctrlKey && event.shiftKey && event.key === 'M') {
                event.preventDefault();
                this.toggle();
            }
            
            // ESC: 关闭面板
            if (event.key === 'Escape' && this.isVisible) {
                this.hide();
            }
        });
        
        // 监听系统初始化完成事件
        window.addEventListener('systemInitialized', () => {
            this.refreshData();
        });
    }
    
    /**
     * 显示状态面板
     */
    show() {
        if (this.isVisible) return;
        
        this.isVisible = true;
        this.panel.classList.remove('hidden');
        
        // 开始自动更新
        this.startAutoUpdate();
        
        // 立即刷新数据
        this.refreshData();
        
        if (window.logger) {
            window.logger.info('系统状态面板已显示', {}, 'status-panel');
        }
    }
    
    /**
     * 隐藏状态面板
     */
    hide() {
        if (!this.isVisible) return;
        
        this.isVisible = false;
        this.panel.classList.add('hidden');
        
        // 停止自动更新
        this.stopAutoUpdate();
        
        if (window.logger) {
            window.logger.info('系统状态面板已隐藏', {}, 'status-panel');
        }
    }
    
    /**
     * 切换面板显示状态
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }
    
    /**
     * 开始自动更新
     */
    startAutoUpdate() {
        if (this.updateInterval) return;
        
        this.updateInterval = setInterval(() => {
            this.refreshData();
        }, this.refreshRate);
        
        // 注册到资源管理器
        if (window.resourceManager) {
            window.resourceManager.addInterval(this.updateInterval, 'status-panel-update');
        }
    }
    
    /**
     * 停止自动更新
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
    
    /**
     * 刷新状态数据
     */
    refreshData() {
        // 收集系统状态数据
        this.collectSystemData();
        this.collectPerformanceData();
        this.collectErrorData();
        this.collectResourceData();
        this.collectSecurityData();
        
        // 更新面板内容
        this.updatePanelContent();
    }
    
    /**
     * 收集系统状态数据
     */
    collectSystemData() {
        this.statusData.system = {
            initialized: window.systemInitializer?.initialized || false,
            version: window.appConfig?.get('system.version', 'Unknown'),
            environment: window.appConfig?.get('system.environment', 'Unknown'),
            debug: window.appConfig?.get('system.debug', false),
            uptime: this.getUptime(),
            timestamp: new Date().toISOString()
        };
    }
    
    /**
     * 收集性能数据
     */
    collectPerformanceData() {
        if (window.performanceMonitor) {
            const report = window.performanceMonitor.getReport();
            this.statusData.performance = {
                monitoring: report.monitoring,
                summary: report.summary,
                dataPoints: report.dataPoints,
                memory: window.performanceMonitor.metrics.memory,
                network: window.performanceMonitor.metrics.network
            };
        } else {
            this.statusData.performance = { available: false };
        }
    }
    
    /**
     * 收集错误数据
     */
    collectErrorData() {
        if (window.enhancedErrorHandler) {
            this.statusData.errors = window.enhancedErrorHandler.getStats();
        } else if (window.logger) {
            this.statusData.errors = {
                recentErrors: window.logger.getRecentErrors(5),
                total: window.logger.logs.filter(log => log.level === 'ERROR').length
            };
        } else {
            this.statusData.errors = { available: false };
        }
    }
    
    /**
     * 收集资源数据
     */
    collectResourceData() {
        if (window.resourceManager) {
            this.statusData.resources = window.resourceManager.getStats();
        } else {
            this.statusData.resources = { available: false };
        }
    }
    
    /**
     * 收集安全数据
     */
    collectSecurityData() {
        if (window.securityUtils) {
            this.statusData.security = window.securityUtils.getSecurityStats();
        } else {
            this.statusData.security = { available: false };
        }
    }
    
    /**
     * 更新面板内容
     */
    updatePanelContent() {
        const html = `
            ${this.renderSystemSection()}
            ${this.renderPerformanceSection()}
            ${this.renderErrorSection()}
            ${this.renderResourceSection()}
            ${this.renderSecuritySection()}
        `;
        
        this.content.innerHTML = html;
    }
    
    /**
     * 渲染系统状态部分
     */
    renderSystemSection() {
        const data = this.statusData.system;
        const statusBadge = data.initialized ? 
            '<span class="status-badge success">运行中</span>' : 
            '<span class="status-badge warning">初始化中</span>';
        
        return `
            <div class="status-section">
                <div class="status-section-header">
                    <i class="fas fa-cogs"></i>
                    系统状态 ${statusBadge}
                </div>
                <div class="status-section-content">
                    <div class="status-grid">
                        <div class="status-item">
                            <span class="status-label">版本</span>
                            <span class="status-value">${data.version}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">环境</span>
                            <span class="status-value">${data.environment}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">调试模式</span>
                            <span class="status-value ${data.debug ? 'warning' : 'good'}">${data.debug ? '开启' : '关闭'}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">运行时间</span>
                            <span class="status-value">${data.uptime}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 渲染性能状态部分
     */
    renderPerformanceSection() {
        const data = this.statusData.performance;
        
        if (!data.available) {
            return `
                <div class="status-section">
                    <div class="status-section-header">
                        <i class="fas fa-tachometer-alt"></i>
                        性能监控 <span class="status-badge warning">不可用</span>
                    </div>
                    <div class="status-section-content">
                        <p>性能监控模块未加载</p>
                    </div>
                </div>
            `;
        }
        
        const memoryUsage = data.memory?.usagePercentage || 'N/A';
        const networkType = data.network?.effectiveType || 'Unknown';
        
        return `
            <div class="status-section">
                <div class="status-section-header">
                    <i class="fas fa-tachometer-alt"></i>
                    性能监控 <span class="status-badge ${data.monitoring ? 'success' : 'warning'}">${data.monitoring ? '运行中' : '已停止'}</span>
                </div>
                <div class="status-section-content">
                    <div class="status-grid">
                        <div class="status-item">
                            <span class="status-label">内存使用率</span>
                            <span class="status-value ${this.getMemoryStatusClass(memoryUsage)}">${memoryUsage}%</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">网络类型</span>
                            <span class="status-value">${networkType}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">数据点</span>
                            <span class="status-value">${data.dataPoints || 0}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 渲染错误状态部分
     */
    renderErrorSection() {
        const data = this.statusData.errors;
        
        if (!data.available) {
            return `
                <div class="status-section">
                    <div class="status-section-header">
                        <i class="fas fa-exclamation-triangle"></i>
                        错误监控 <span class="status-badge warning">不可用</span>
                    </div>
                    <div class="status-section-content">
                        <p>错误监控模块未加载</p>
                    </div>
                </div>
            `;
        }
        
        const errorCount = data.total || 0;
        const successRate = data.successRate || '0%';
        
        return `
            <div class="status-section">
                <div class="status-section-header">
                    <i class="fas fa-exclamation-triangle"></i>
                    错误监控 <span class="status-badge ${errorCount === 0 ? 'success' : 'warning'}">总计 ${errorCount} 个错误</span>
                </div>
                <div class="status-section-content">
                    <div class="status-grid">
                        <div class="status-item">
                            <span class="status-label">成功率</span>
                            <span class="status-value good">${successRate}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">已解决</span>
                            <span class="status-value">${data.resolved || 0}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">未解决</span>
                            <span class="status-value ${data.unresolved > 0 ? 'error' : 'good'}">${data.unresolved || 0}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 渲染资源状态部分
     */
    renderResourceSection() {
        const data = this.statusData.resources;
        
        if (!data.available) {
            return `
                <div class="status-section">
                    <div class="status-section-header">
                        <i class="fas fa-memory"></i>
                        资源管理 <span class="status-badge warning">不可用</span>
                    </div>
                    <div class="status-section-content">
                        <p>资源管理模块未加载</p>
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="status-section">
                <div class="status-section-header">
                    <i class="fas fa-memory"></i>
                    资源管理 <span class="status-badge success">正常</span>
                </div>
                <div class="status-section-content">
                    <div class="status-grid">
                        <div class="status-item">
                            <span class="status-label">定时器</span>
                            <span class="status-value">${data.timers || 0}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">事件监听器</span>
                            <span class="status-value">${data.listeners || 0}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">观察器</span>
                            <span class="status-value">${data.observers || 0}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">网络请求</span>
                            <span class="status-value">${data.requests || 0}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 渲染安全状态部分
     */
    renderSecuritySection() {
        const data = this.statusData.security;
        
        if (!data.available) {
            return `
                <div class="status-section">
                    <div class="status-section-header">
                        <i class="fas fa-shield-alt"></i>
                        安全状态 <span class="status-badge warning">不可用</span>
                    </div>
                    <div class="status-section-content">
                        <p>安全模块未加载</p>
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="status-section">
                <div class="status-section-header">
                    <i class="fas fa-shield-alt"></i>
                    安全状态 <span class="status-badge ${data.cspEnabled ? 'success' : 'warning'}">${data.cspEnabled ? '安全' : '需要关注'}</span>
                </div>
                <div class="status-section-content">
                    <div class="status-grid">
                        <div class="status-item">
                            <span class="status-label">CSP策略</span>
                            <span class="status-value ${data.cspEnabled ? 'good' : 'warning'}">${data.cspEnabled ? '已启用' : '未启用'}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">XSS防护</span>
                            <span class="status-value good">已启用</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">域名白名单</span>
                            <span class="status-value">${data.allowedDomains || 0} 个</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 获取内存状态样式类
     */
    getMemoryStatusClass(usage) {
        const numUsage = parseFloat(usage);
        if (numUsage < 50) return 'good';
        if (numUsage < 80) return 'warning';
        return 'error';
    }
    
    /**
     * 获取系统运行时间
     */
    getUptime() {
        const startTime = window.systemStartTime || Date.now();
        const uptime = Date.now() - startTime;
        
        const seconds = Math.floor(uptime / 1000) % 60;
        const minutes = Math.floor(uptime / (1000 * 60)) % 60;
        const hours = Math.floor(uptime / (1000 * 60 * 60));
        
        if (hours > 0) {
            return `${hours}小时 ${minutes}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分钟 ${seconds}秒`;
        } else {
            return `${seconds}秒`;
        }
    }
    
    /**
     * 导出状态报告
     */
    exportReport() {
        const report = {
            timestamp: new Date().toISOString(),
            system: this.statusData.system,
            performance: this.statusData.performance,
            errors: this.statusData.errors,
            resources: this.statusData.resources,
            security: this.statusData.security
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `system-status-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        
        if (window.logger) {
            window.logger.info('系统状态报告已导出', {}, 'status-panel');
        }
    }
}

// 创建全局状态面板实例
window.SystemStatusPanel = SystemStatusPanel;
window.systemStatusPanel = new SystemStatusPanel();

// 记录系统启动时间
window.systemStartTime = Date.now();

// 暴露到全局作用域
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SystemStatusPanel;
}
