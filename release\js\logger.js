/**
 * 统一日志系统
 * 提供结构化日志记录、错误追踪和日志管理功能
 */
class Logger {
    constructor() {
        this.levels = {
            ERROR: 0,
            WARN: 1,
            INFO: 2,
            DEBUG: 3
        };
        
        this.levelNames = ['ERROR', 'WARN', 'INFO', 'DEBUG'];
        this.logs = [];
        this.maxLogs = 1000;
        this.currentLevel = this.levels.INFO;
        
        // 日志输出配置
        this.outputs = {
            console: true,
            storage: true,
            remote: false
        };
        
        // 错误统计
        this.errorStats = {
            total: 0,
            byType: {},
            byModule: {}
        };
        
        // 性能监控
        this.performanceMarks = new Map();
        
        this.init();
    }
    
    /**
     * 初始化日志系统
     */
    init() {
        // 从配置管理器获取日志级别
        if (window.appConfig) {
            this.currentLevel = window.appConfig.get('system.debug') ? this.levels.DEBUG : this.levels.INFO;
            
            // 监听配置变更
            window.appConfig.onChange('system.debug', (debug) => {
                this.currentLevel = debug ? this.levels.DEBUG : this.levels.INFO;
                this.info('日志级别已更新', { level: this.levelNames[this.currentLevel] });
            });
        }
        
        // 捕获全局错误
        this.setupGlobalErrorHandling();
        
        // 从本地存储恢复日志
        this.loadFromStorage();
        
        this.info('日志系统已初始化', { 
            level: this.levelNames[this.currentLevel],
            maxLogs: this.maxLogs 
        });
    }
    
    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // 捕获未处理的错误
        window.addEventListener('error', (event) => {
            this.error('全局错误', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack
            });
        });
        
        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.error('未处理的Promise拒绝', {
                reason: event.reason,
                stack: event.reason?.stack
            });
        });
    }
    
    /**
     * 记录错误日志
     * @param {string} message - 日志消息
     * @param {object} context - 上下文信息
     * @param {string} module - 模块名称
     */
    error(message, context = {}, module = 'unknown') {
        this.log('ERROR', message, context, module);
        
        // 更新错误统计
        this.errorStats.total++;
        this.errorStats.byModule[module] = (this.errorStats.byModule[module] || 0) + 1;
        
        // 如果有错误类型，也统计
        if (context.type) {
            this.errorStats.byType[context.type] = (this.errorStats.byType[context.type] || 0) + 1;
        }
        
        // 发送到远程错误监控服务（如果配置了）
        this.sendToRemoteIfEnabled('error', { message, context, module });
    }
    
    /**
     * 记录警告日志
     * @param {string} message - 日志消息
     * @param {object} context - 上下文信息
     * @param {string} module - 模块名称
     */
    warn(message, context = {}, module = 'unknown') {
        this.log('WARN', message, context, module);
    }
    
    /**
     * 记录信息日志
     * @param {string} message - 日志消息
     * @param {object} context - 上下文信息
     * @param {string} module - 模块名称
     */
    info(message, context = {}, module = 'unknown') {
        this.log('INFO', message, context, module);
    }
    
    /**
     * 记录调试日志
     * @param {string} message - 日志消息
     * @param {object} context - 上下文信息
     * @param {string} module - 模块名称
     */
    debug(message, context = {}, module = 'unknown') {
        this.log('DEBUG', message, context, module);
    }
    
    /**
     * 核心日志记录方法
     * @param {string} level - 日志级别
     * @param {string} message - 日志消息
     * @param {object} context - 上下文信息
     * @param {string} module - 模块名称
     */
    log(level, message, context = {}, module = 'unknown') {
        const levelValue = this.levels[level];
        
        // 检查日志级别
        if (levelValue > this.currentLevel) {
            return;
        }
        
        const logEntry = {
            id: this.generateLogId(),
            timestamp: new Date().toISOString(),
            level: level,
            message: message,
            context: context,
            module: module,
            url: window.location.href,
            userAgent: navigator.userAgent,
            sessionId: this.getSessionId()
        };
        
        // 添加到日志数组
        this.logs.push(logEntry);
        
        // 保持日志数量限制
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
        
        // 输出到控制台
        if (this.outputs.console) {
            this.outputToConsole(logEntry);
        }
        
        // 保存到本地存储
        if (this.outputs.storage) {
            this.saveToStorage();
        }
        
        // 发送到远程服务
        if (this.outputs.remote) {
            this.sendToRemoteIfEnabled('log', logEntry);
        }
    }
    
    /**
     * 输出到控制台
     * @param {object} logEntry - 日志条目
     */
    outputToConsole(logEntry) {
        const { level, message, context, module, timestamp } = logEntry;
        const timeStr = new Date(timestamp).toLocaleTimeString();
        const prefix = `[${timeStr}] [${level}] [${module}]`;
        
        const consoleMethod = {
            'ERROR': 'error',
            'WARN': 'warn',
            'INFO': 'info',
            'DEBUG': 'debug'
        }[level] || 'log';
        
        if (Object.keys(context).length > 0) {
            console[consoleMethod](prefix, message, context);
        } else {
            console[consoleMethod](prefix, message);
        }
    }
    
    /**
     * 开始性能计时
     * @param {string} name - 计时器名称
     */
    startTimer(name) {
        this.performanceMarks.set(name, {
            start: performance.now(),
            startTime: Date.now()
        });
        this.debug('性能计时开始', { timer: name });
    }
    
    /**
     * 结束性能计时
     * @param {string} name - 计时器名称
     * @param {object} context - 额外上下文
     */
    endTimer(name, context = {}) {
        const mark = this.performanceMarks.get(name);
        if (!mark) {
            this.warn('性能计时器不存在', { timer: name });
            return;
        }
        
        const duration = performance.now() - mark.start;
        this.performanceMarks.delete(name);
        
        this.info('性能计时结束', {
            timer: name,
            duration: `${duration.toFixed(2)}ms`,
            ...context
        });
        
        return duration;
    }
    
    /**
     * 记录用户操作
     * @param {string} action - 操作名称
     * @param {object} details - 操作详情
     */
    logUserAction(action, details = {}) {
        this.info('用户操作', {
            action: action,
            details: details,
            timestamp: Date.now()
        }, 'user-action');
    }
    
    /**
     * 记录系统事件
     * @param {string} event - 事件名称
     * @param {object} data - 事件数据
     */
    logSystemEvent(event, data = {}) {
        this.info('系统事件', {
            event: event,
            data: data
        }, 'system');
    }
    
    /**
     * 获取日志统计信息
     * @returns {object} 统计信息
     */
    getStats() {
        const levelCounts = {};
        this.levelNames.forEach(level => {
            levelCounts[level] = this.logs.filter(log => log.level === level).length;
        });
        
        return {
            totalLogs: this.logs.length,
            levelCounts: levelCounts,
            errorStats: this.errorStats,
            oldestLog: this.logs.length > 0 ? this.logs[0].timestamp : null,
            newestLog: this.logs.length > 0 ? this.logs[this.logs.length - 1].timestamp : null
        };
    }
    
    /**
     * 搜索日志
     * @param {object} criteria - 搜索条件
     * @returns {array} 匹配的日志
     */
    search(criteria = {}) {
        return this.logs.filter(log => {
            if (criteria.level && log.level !== criteria.level) return false;
            if (criteria.module && log.module !== criteria.module) return false;
            if (criteria.message && !log.message.toLowerCase().includes(criteria.message.toLowerCase())) return false;
            if (criteria.startTime && new Date(log.timestamp) < new Date(criteria.startTime)) return false;
            if (criteria.endTime && new Date(log.timestamp) > new Date(criteria.endTime)) return false;
            return true;
        });
    }
    
    /**
     * 导出日志
     * @param {string} format - 导出格式 ('json' | 'csv' | 'txt')
     * @returns {string} 导出的日志数据
     */
    export(format = 'json') {
        switch (format) {
            case 'json':
                return JSON.stringify(this.logs, null, 2);
            case 'csv':
                return this.exportToCSV();
            case 'txt':
                return this.exportToText();
            default:
                throw new Error('不支持的导出格式');
        }
    }
    
    /**
     * 导出为CSV格式
     * @returns {string} CSV数据
     */
    exportToCSV() {
        const headers = ['timestamp', 'level', 'module', 'message', 'context'];
        const rows = this.logs.map(log => [
            log.timestamp,
            log.level,
            log.module,
            log.message,
            JSON.stringify(log.context)
        ]);
        
        return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    /**
     * 导出为文本格式
     * @returns {string} 文本数据
     */
    exportToText() {
        return this.logs.map(log => {
            const time = new Date(log.timestamp).toLocaleString();
            const context = Object.keys(log.context).length > 0 ? 
                ` | ${JSON.stringify(log.context)}` : '';
            return `[${time}] [${log.level}] [${log.module}] ${log.message}${context}`;
        }).join('\n');
    }
    
    /**
     * 清除日志
     * @param {object} criteria - 清除条件
     */
    clear(criteria = {}) {
        if (Object.keys(criteria).length === 0) {
            // 清除所有日志
            this.logs = [];
            this.errorStats = { total: 0, byType: {}, byModule: {} };
        } else {
            // 根据条件清除
            this.logs = this.logs.filter(log => !this.matchesCriteria(log, criteria));
        }
        
        this.saveToStorage();
        this.info('日志已清除', criteria);
    }
    
    /**
     * 检查日志是否匹配条件
     * @param {object} log - 日志条目
     * @param {object} criteria - 匹配条件
     * @returns {boolean} 是否匹配
     */
    matchesCriteria(log, criteria) {
        if (criteria.level && log.level !== criteria.level) return false;
        if (criteria.module && log.module !== criteria.module) return false;
        if (criteria.olderThan && new Date(log.timestamp) > new Date(criteria.olderThan)) return false;
        return true;
    }
    
    /**
     * 保存到本地存储
     */
    saveToStorage() {
        try {
            const recentLogs = this.logs.slice(-100); // 只保存最近100条
            localStorage.setItem('intelligent-presentation-logs', JSON.stringify(recentLogs));
        } catch (error) {
            console.warn('保存日志到本地存储失败:', error);
        }
    }
    
    /**
     * 从本地存储加载
     */
    loadFromStorage() {
        try {
            const stored = localStorage.getItem('intelligent-presentation-logs');
            if (stored) {
                const storedLogs = JSON.parse(stored);
                this.logs = storedLogs;
            }
        } catch (error) {
            console.warn('从本地存储加载日志失败:', error);
        }
    }
    
    /**
     * 发送到远程服务（如果启用）
     * @param {string} type - 数据类型
     * @param {object} data - 数据
     */
    sendToRemoteIfEnabled(type, data) {
        // 这里可以集成第三方日志服务，如 Sentry、LogRocket 等
        if (this.outputs.remote && window.appConfig?.get('system.environment') === 'production') {
            // 实现远程日志发送逻辑
            console.debug('发送日志到远程服务:', type, data);
        }
    }
    
    /**
     * 生成日志ID
     * @returns {string} 唯一ID
     */
    generateLogId() {
        return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 获取会话ID
     * @returns {string} 会话ID
     */
    getSessionId() {
        if (!this.sessionId) {
            this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        return this.sessionId;
    }
    
    /**
     * 设置日志级别
     * @param {string} level - 日志级别
     */
    setLevel(level) {
        if (this.levels.hasOwnProperty(level)) {
            this.currentLevel = this.levels[level];
            this.info('日志级别已更新', { level: level });
        } else {
            this.warn('无效的日志级别', { level: level });
        }
    }
    
    /**
     * 获取最近的错误日志
     * @param {number} count - 数量
     * @returns {array} 错误日志
     */
    getRecentErrors(count = 10) {
        return this.logs
            .filter(log => log.level === 'ERROR')
            .slice(-count)
            .reverse();
    }
}

// 创建全局日志实例
window.Logger = Logger;
window.logger = new Logger();

// 暴露到全局作用域
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Logger;
}
