/**
 * 安全工具类
 * 提供输入验证、XSS防护、CSP策略等安全功能
 */
class SecurityUtils {
    constructor() {
        // XSS防护配置
        this.xssConfig = {
            allowedTags: ['b', 'i', 'em', 'strong', 'span'],
            allowedAttributes: ['class', 'id'],
            maxLength: 10000
        };
        
        // URL白名单
        this.allowedDomains = [
            'localhost',
            '127.0.0.1',
            'agmpuat.yunfeiyun.com'
        ];
        
        // 危险函数检测
        this.dangerousFunctions = [
            'eval',
            'Function',
            'setTimeout',
            'setInterval',
            'execScript'
        ];
        
        this.init();
    }
    
    /**
     * 初始化安全工具
     */
    init() {
        // 设置CSP策略
        this.setupCSP();
        
        // 监听安全事件
        this.setupSecurityEventListeners();
        
        if (window.logger) {
            window.logger.info('安全工具已初始化', {
                allowedDomains: this.allowedDomains.length,
                xssProtection: true,
                cspEnabled: true
            }, 'security');
        }
    }
    
    /**
     * 设置内容安全策略
     */
    setupCSP() {
        // 检查是否已有CSP策略
        const existingCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
        
        if (!existingCSP && window.appConfig?.get('security.enableCSP')) {
            const cspMeta = document.createElement('meta');
            cspMeta.httpEquiv = 'Content-Security-Policy';
            cspMeta.content = this.generateCSPPolicy();
            document.head.appendChild(cspMeta);
            
            if (window.logger) {
                window.logger.info('CSP策略已设置', { policy: cspMeta.content }, 'security');
            }
        }
    }
    
    /**
     * 生成CSP策略
     * @returns {string} CSP策略字符串
     */
    generateCSPPolicy() {
        return [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https:",
            "font-src 'self' data:",
            "connect-src 'self' https:",
            "frame-src 'self' https:",
            "object-src 'none'",
            "base-uri 'self'"
        ].join('; ');
    }
    
    /**
     * 设置安全事件监听器
     */
    setupSecurityEventListeners() {
        // 监听CSP违规
        document.addEventListener('securitypolicyviolation', (event) => {
            if (window.logger) {
                window.logger.error('CSP策略违规', {
                    violatedDirective: event.violatedDirective,
                    blockedURI: event.blockedURI,
                    documentURI: event.documentURI,
                    lineNumber: event.lineNumber
                }, 'security');
            }
        });
    }
    
    /**
     * HTML内容清理（XSS防护）
     * @param {string} input - 输入内容
     * @param {object} options - 清理选项
     * @returns {string} 清理后的内容
     */
    sanitizeHTML(input, options = {}) {
        if (typeof input !== 'string') {
            return '';
        }
        
        const config = { ...this.xssConfig, ...options };
        
        // 长度检查
        if (input.length > config.maxLength) {
            if (window.logger) {
                window.logger.warn('输入内容超长', { 
                    length: input.length, 
                    maxLength: config.maxLength 
                }, 'security');
            }
            input = input.substring(0, config.maxLength);
        }
        
        // 创建临时DOM元素进行清理
        const tempDiv = document.createElement('div');
        tempDiv.textContent = input;
        
        // 如果允许某些HTML标签，进行更精细的处理
        if (config.allowedTags.length > 0) {
            return this.sanitizeWithAllowedTags(input, config);
        }
        
        return tempDiv.innerHTML;
    }
    
    /**
     * 使用允许的标签进行HTML清理
     * @param {string} input - 输入内容
     * @param {object} config - 配置
     * @returns {string} 清理后的内容
     */
    sanitizeWithAllowedTags(input, config) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = input;
        
        // 递归清理元素
        this.cleanElement(tempDiv, config);
        
        return tempDiv.innerHTML;
    }
    
    /**
     * 清理DOM元素
     * @param {Element} element - DOM元素
     * @param {object} config - 配置
     */
    cleanElement(element, config) {
        const children = Array.from(element.children);
        
        children.forEach(child => {
            const tagName = child.tagName.toLowerCase();
            
            // 检查标签是否允许
            if (!config.allowedTags.includes(tagName)) {
                // 移除标签但保留内容
                const textContent = child.textContent;
                const textNode = document.createTextNode(textContent);
                child.parentNode.replaceChild(textNode, child);
                return;
            }
            
            // 清理属性
            const attributes = Array.from(child.attributes);
            attributes.forEach(attr => {
                if (!config.allowedAttributes.includes(attr.name)) {
                    child.removeAttribute(attr.name);
                }
            });
            
            // 递归处理子元素
            this.cleanElement(child, config);
        });
    }
    
    /**
     * URL验证
     * @param {string} url - URL字符串
     * @param {boolean} allowRelative - 是否允许相对URL
     * @returns {boolean} 是否有效
     */
    validateURL(url, allowRelative = false) {
        if (typeof url !== 'string' || url.trim() === '') {
            return false;
        }
        
        // 允许相对URL
        if (allowRelative && (url.startsWith('/') || url.startsWith('./'))) {
            return true;
        }
        
        try {
            const urlObj = new URL(url);
            
            // 检查协议
            if (!['http:', 'https:'].includes(urlObj.protocol)) {
                return false;
            }
            
            // 检查域名白名单
            if (this.allowedDomains.length > 0) {
                const hostname = urlObj.hostname;
                const isAllowed = this.allowedDomains.some(domain => {
                    return hostname === domain || hostname.endsWith('.' + domain);
                });
                
                if (!isAllowed) {
                    if (window.logger) {
                        window.logger.warn('URL域名不在白名单中', { 
                            url: url, 
                            hostname: hostname 
                        }, 'security');
                    }
                    return false;
                }
            }
            
            return true;
        } catch (error) {
            if (window.logger) {
                window.logger.warn('URL格式无效', { 
                    url: url, 
                    error: error.message 
                }, 'security');
            }
            return false;
        }
    }
    
    /**
     * CSS选择器验证
     * @param {string} selector - CSS选择器
     * @returns {boolean} 是否有效
     */
    validateSelector(selector) {
        if (typeof selector !== 'string' || selector.trim() === '') {
            return false;
        }
        
        // 检查危险字符
        const dangerousPatterns = [
            /javascript:/i,
            /data:/i,
            /vbscript:/i,
            /<script/i,
            /on\w+\s*=/i
        ];
        
        for (const pattern of dangerousPatterns) {
            if (pattern.test(selector)) {
                if (window.logger) {
                    window.logger.warn('选择器包含危险模式', { 
                        selector: selector 
                    }, 'security');
                }
                return false;
            }
        }
        
        try {
            // 尝试使用选择器
            document.querySelector(selector);
            return true;
        } catch (error) {
            if (window.logger) {
                window.logger.warn('选择器语法无效', { 
                    selector: selector, 
                    error: error.message 
                }, 'security');
            }
            return false;
        }
    }
    
    /**
     * 脚本内容验证
     * @param {string} script - 脚本内容
     * @returns {boolean} 是否安全
     */
    validateScript(script) {
        if (typeof script !== 'string') {
            return false;
        }
        
        // 检查危险函数
        for (const dangerousFunc of this.dangerousFunctions) {
            if (script.includes(dangerousFunc)) {
                if (window.logger) {
                    window.logger.warn('脚本包含危险函数', { 
                        function: dangerousFunc,
                        script: script.substring(0, 100) + '...'
                    }, 'security');
                }
                return false;
            }
        }
        
        // 检查危险模式
        const dangerousPatterns = [
            /document\.write/i,
            /innerHTML\s*=/i,
            /outerHTML\s*=/i,
            /location\s*=/i,
            /window\.open/i
        ];
        
        for (const pattern of dangerousPatterns) {
            if (pattern.test(script)) {
                if (window.logger) {
                    window.logger.warn('脚本包含危险模式', { 
                        pattern: pattern.toString(),
                        script: script.substring(0, 100) + '...'
                    }, 'security');
                }
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 输入长度验证
     * @param {string} input - 输入内容
     * @param {number} maxLength - 最大长度
     * @returns {boolean} 是否有效
     */
    validateLength(input, maxLength = 1000) {
        if (typeof input !== 'string') {
            return false;
        }
        
        return input.length <= maxLength;
    }
    
    /**
     * 数字验证
     * @param {*} value - 值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {boolean} 是否有效
     */
    validateNumber(value, min = -Infinity, max = Infinity) {
        const num = Number(value);
        
        if (isNaN(num) || !isFinite(num)) {
            return false;
        }
        
        return num >= min && num <= max;
    }
    
    /**
     * 对象属性验证
     * @param {object} obj - 对象
     * @param {object} schema - 验证模式
     * @returns {object} 验证结果
     */
    validateObject(obj, schema) {
        const result = {
            valid: true,
            errors: []
        };
        
        if (typeof obj !== 'object' || obj === null) {
            result.valid = false;
            result.errors.push('输入不是有效对象');
            return result;
        }
        
        for (const [key, rules] of Object.entries(schema)) {
            const value = obj[key];
            
            // 必填检查
            if (rules.required && (value === undefined || value === null)) {
                result.valid = false;
                result.errors.push(`字段 ${key} 是必填的`);
                continue;
            }
            
            // 如果值不存在且不是必填，跳过验证
            if (value === undefined || value === null) {
                continue;
            }
            
            // 类型检查
            if (rules.type && typeof value !== rules.type) {
                result.valid = false;
                result.errors.push(`字段 ${key} 类型应为 ${rules.type}`);
                continue;
            }
            
            // 长度检查
            if (rules.maxLength && typeof value === 'string' && value.length > rules.maxLength) {
                result.valid = false;
                result.errors.push(`字段 ${key} 长度不能超过 ${rules.maxLength}`);
            }
            
            // 数值范围检查
            if (rules.min !== undefined && typeof value === 'number' && value < rules.min) {
                result.valid = false;
                result.errors.push(`字段 ${key} 不能小于 ${rules.min}`);
            }
            
            if (rules.max !== undefined && typeof value === 'number' && value > rules.max) {
                result.valid = false;
                result.errors.push(`字段 ${key} 不能大于 ${rules.max}`);
            }
            
            // 自定义验证函数
            if (rules.validator && typeof rules.validator === 'function') {
                if (!rules.validator(value)) {
                    result.valid = false;
                    result.errors.push(`字段 ${key} 验证失败`);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 生成安全的随机字符串
     * @param {number} length - 长度
     * @returns {string} 随机字符串
     */
    generateSecureToken(length = 32) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        
        // 使用crypto API生成安全随机数
        if (window.crypto && window.crypto.getRandomValues) {
            const array = new Uint8Array(length);
            window.crypto.getRandomValues(array);
            
            for (let i = 0; i < length; i++) {
                result += chars[array[i] % chars.length];
            }
        } else {
            // 降级到Math.random
            for (let i = 0; i < length; i++) {
                result += chars[Math.floor(Math.random() * chars.length)];
            }
        }
        
        return result;
    }
    
    /**
     * 添加允许的域名
     * @param {string} domain - 域名
     */
    addAllowedDomain(domain) {
        if (typeof domain === 'string' && !this.allowedDomains.includes(domain)) {
            this.allowedDomains.push(domain);
            
            if (window.logger) {
                window.logger.info('添加允许的域名', { domain: domain }, 'security');
            }
        }
    }
    
    /**
     * 移除允许的域名
     * @param {string} domain - 域名
     */
    removeAllowedDomain(domain) {
        const index = this.allowedDomains.indexOf(domain);
        if (index > -1) {
            this.allowedDomains.splice(index, 1);
            
            if (window.logger) {
                window.logger.info('移除允许的域名', { domain: domain }, 'security');
            }
        }
    }
    
    /**
     * 获取安全统计信息
     * @returns {object} 统计信息
     */
    getSecurityStats() {
        return {
            allowedDomains: this.allowedDomains.length,
            xssProtection: true,
            cspEnabled: !!document.querySelector('meta[http-equiv="Content-Security-Policy"]'),
            dangerousFunctions: this.dangerousFunctions.length
        };
    }
    
    /**
     * 执行安全检查
     * @param {object} context - 检查上下文
     * @returns {object} 检查结果
     */
    performSecurityCheck(context = {}) {
        const checks = {
            csp: !!document.querySelector('meta[http-equiv="Content-Security-Policy"]'),
            https: location.protocol === 'https:',
            xssProtection: true,
            domainWhitelist: this.allowedDomains.length > 0
        };
        
        const passed = Object.values(checks).filter(Boolean).length;
        const total = Object.keys(checks).length;
        
        const result = {
            checks: checks,
            score: (passed / total * 100).toFixed(1),
            passed: passed,
            total: total,
            recommendations: []
        };
        
        // 生成建议
        if (!checks.csp) {
            result.recommendations.push('建议启用内容安全策略(CSP)');
        }
        
        if (!checks.https && location.hostname !== 'localhost') {
            result.recommendations.push('建议使用HTTPS协议');
        }
        
        if (!checks.domainWhitelist) {
            result.recommendations.push('建议配置域名白名单');
        }
        
        if (window.logger) {
            window.logger.info('安全检查完成', result, 'security');
        }
        
        return result;
    }
}

// 创建全局安全工具实例
window.SecurityUtils = SecurityUtils;
window.securityUtils = new SecurityUtils();

// 暴露到全局作用域
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecurityUtils;
}
