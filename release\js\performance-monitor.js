/**
 * 性能监控工具
 * 提供性能指标收集、分析和优化建议
 */
class PerformanceMonitor {
    constructor() {
        // 性能指标
        this.metrics = {
            pageLoad: {},
            userInteraction: {},
            memory: {},
            network: {},
            rendering: {}
        };
        
        // 性能阈值配置
        this.thresholds = {
            pageLoadTime: 3000,      // 页面加载时间 (ms)
            firstContentfulPaint: 1500, // 首次内容绘制 (ms)
            largestContentfulPaint: 2500, // 最大内容绘制 (ms)
            cumulativeLayoutShift: 0.1,   // 累积布局偏移
            firstInputDelay: 100,    // 首次输入延迟 (ms)
            memoryUsage: 50 * 1024 * 1024, // 内存使用 (bytes)
            networkLatency: 1000     // 网络延迟 (ms)
        };
        
        // 性能观察器
        this.observers = new Map();
        
        // 性能数据收集
        this.performanceData = [];
        this.maxDataPoints = 1000;
        
        // 监控状态
        this.isMonitoring = false;
        this.monitoringInterval = null;
        
        this.init();
    }
    
    /**
     * 初始化性能监控
     */
    init() {
        // 检查浏览器支持
        if (!this.checkBrowserSupport()) {
            if (window.logger) {
                window.logger.warn('浏览器不支持完整的性能监控功能', {}, 'performance');
            }
            return;
        }
        
        // 收集页面加载性能
        this.collectPageLoadMetrics();
        
        // 设置性能观察器
        this.setupPerformanceObservers();
        
        // 开始监控
        this.startMonitoring();
        
        if (window.logger) {
            window.logger.info('性能监控已启动', {
                thresholds: this.thresholds,
                observersCount: this.observers.size
            }, 'performance');
        }
    }
    
    /**
     * 检查浏览器支持
     * @returns {boolean} 是否支持
     */
    checkBrowserSupport() {
        return !!(
            window.performance &&
            window.performance.timing &&
            window.performance.now
        );
    }
    
    /**
     * 收集页面加载性能指标
     */
    collectPageLoadMetrics() {
        if (!performance.timing) return;
        
        const timing = performance.timing;
        const navigation = performance.navigation;
        
        this.metrics.pageLoad = {
            // 导航类型
            navigationType: navigation.type,
            redirectCount: navigation.redirectCount,
            
            // 时间指标
            domainLookupTime: timing.domainLookupEnd - timing.domainLookupStart,
            connectTime: timing.connectEnd - timing.connectStart,
            requestTime: timing.responseEnd - timing.requestStart,
            responseTime: timing.responseEnd - timing.responseStart,
            domProcessingTime: timing.domContentLoadedEventStart - timing.domLoading,
            domReadyTime: timing.domContentLoadedEventEnd - timing.navigationStart,
            loadCompleteTime: timing.loadEventEnd - timing.navigationStart,
            
            // 总体指标
            totalLoadTime: timing.loadEventEnd - timing.navigationStart,
            firstByteTime: timing.responseStart - timing.navigationStart,
            
            // 时间戳
            timestamp: Date.now()
        };
        
        // 记录到性能数据
        this.addPerformanceData('pageLoad', this.metrics.pageLoad);
        
        if (window.logger) {
            window.logger.info('页面加载性能', this.metrics.pageLoad, 'performance');
        }
    }
    
    /**
     * 设置性能观察器
     */
    setupPerformanceObservers() {
        // Paint 性能观察器
        if (window.PerformanceObserver) {
            try {
                const paintObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.metrics.rendering[entry.name] = entry.startTime;
                        this.addPerformanceData('paint', {
                            name: entry.name,
                            startTime: entry.startTime,
                            timestamp: Date.now()
                        });
                    }
                });
                paintObserver.observe({ entryTypes: ['paint'] });
                this.observers.set('paint', paintObserver);
            } catch (error) {
                console.warn('Paint observer setup failed:', error);
            }
            
            // Layout Shift 观察器
            try {
                const layoutShiftObserver = new PerformanceObserver((list) => {
                    let cumulativeScore = 0;
                    for (const entry of list.getEntries()) {
                        if (!entry.hadRecentInput) {
                            cumulativeScore += entry.value;
                        }
                    }
                    this.metrics.rendering.cumulativeLayoutShift = cumulativeScore;
                    this.addPerformanceData('layoutShift', {
                        cumulativeScore: cumulativeScore,
                        timestamp: Date.now()
                    });
                });
                layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
                this.observers.set('layoutShift', layoutShiftObserver);
            } catch (error) {
                console.warn('Layout shift observer setup failed:', error);
            }
            
            // Largest Contentful Paint 观察器
            try {
                const lcpObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    this.metrics.rendering.largestContentfulPaint = lastEntry.startTime;
                    this.addPerformanceData('lcp', {
                        startTime: lastEntry.startTime,
                        size: lastEntry.size,
                        timestamp: Date.now()
                    });
                });
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
                this.observers.set('lcp', lcpObserver);
            } catch (error) {
                console.warn('LCP observer setup failed:', error);
            }
            
            // First Input Delay 观察器
            try {
                const fidObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.metrics.userInteraction.firstInputDelay = entry.processingStart - entry.startTime;
                        this.addPerformanceData('fid', {
                            delay: entry.processingStart - entry.startTime,
                            startTime: entry.startTime,
                            timestamp: Date.now()
                        });
                    }
                });
                fidObserver.observe({ entryTypes: ['first-input'] });
                this.observers.set('fid', fidObserver);
            } catch (error) {
                console.warn('FID observer setup failed:', error);
            }
        }
    }
    
    /**
     * 开始性能监控
     */
    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        
        // 定期收集性能数据
        this.monitoringInterval = setInterval(() => {
            this.collectRuntimeMetrics();
        }, 5000); // 每5秒收集一次
        
        // 注册到资源管理器
        if (window.resourceManager) {
            window.resourceManager.addInterval(this.monitoringInterval, 'performance-monitoring');
        }
    }
    
    /**
     * 停止性能监控
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;
        
        this.isMonitoring = false;
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        // 断开所有观察器
        this.observers.forEach(observer => {
            observer.disconnect();
        });
        this.observers.clear();
        
        if (window.logger) {
            window.logger.info('性能监控已停止', {}, 'performance');
        }
    }
    
    /**
     * 收集运行时性能指标
     */
    collectRuntimeMetrics() {
        // 内存使用情况
        if (performance.memory) {
            this.metrics.memory = {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
                usagePercentage: (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit * 100).toFixed(2),
                timestamp: Date.now()
            };
            
            this.addPerformanceData('memory', this.metrics.memory);
            
            // 检查内存阈值
            if (this.metrics.memory.usedJSHeapSize > this.thresholds.memoryUsage) {
                this.triggerPerformanceAlert('memory', 'high_memory_usage', this.metrics.memory);
            }
        }
        
        // 网络连接信息
        if (navigator.connection) {
            this.metrics.network = {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt,
                saveData: navigator.connection.saveData,
                timestamp: Date.now()
            };
            
            this.addPerformanceData('network', this.metrics.network);
        }
        
        // 检查性能阈值
        this.checkPerformanceThresholds();
    }
    
    /**
     * 添加性能数据点
     * @param {string} type - 数据类型
     * @param {object} data - 性能数据
     */
    addPerformanceData(type, data) {
        this.performanceData.push({
            type: type,
            data: data,
            timestamp: Date.now()
        });
        
        // 保持数据点数量限制
        if (this.performanceData.length > this.maxDataPoints) {
            this.performanceData.shift();
        }
    }
    
    /**
     * 检查性能阈值
     */
    checkPerformanceThresholds() {
        const checks = [
            {
                name: 'pageLoadTime',
                value: this.metrics.pageLoad.totalLoadTime,
                threshold: this.thresholds.pageLoadTime,
                message: '页面加载时间过长'
            },
            {
                name: 'firstContentfulPaint',
                value: this.metrics.rendering['first-contentful-paint'],
                threshold: this.thresholds.firstContentfulPaint,
                message: '首次内容绘制时间过长'
            },
            {
                name: 'largestContentfulPaint',
                value: this.metrics.rendering.largestContentfulPaint,
                threshold: this.thresholds.largestContentfulPaint,
                message: '最大内容绘制时间过长'
            },
            {
                name: 'cumulativeLayoutShift',
                value: this.metrics.rendering.cumulativeLayoutShift,
                threshold: this.thresholds.cumulativeLayoutShift,
                message: '累积布局偏移过大'
            },
            {
                name: 'firstInputDelay',
                value: this.metrics.userInteraction.firstInputDelay,
                threshold: this.thresholds.firstInputDelay,
                message: '首次输入延迟过长'
            }
        ];
        
        checks.forEach(check => {
            if (check.value && check.value > check.threshold) {
                this.triggerPerformanceAlert(check.name, 'threshold_exceeded', {
                    value: check.value,
                    threshold: check.threshold,
                    message: check.message
                });
            }
        });
    }
    
    /**
     * 触发性能警报
     * @param {string} metric - 指标名称
     * @param {string} type - 警报类型
     * @param {object} data - 相关数据
     */
    triggerPerformanceAlert(metric, type, data) {
        const alert = {
            metric: metric,
            type: type,
            data: data,
            timestamp: Date.now(),
            recommendations: this.getRecommendations(metric, type, data)
        };
        
        if (window.logger) {
            window.logger.warn('性能警报', alert, 'performance');
        }
        
        // 触发自定义事件
        const event = new CustomEvent('performanceAlert', { detail: alert });
        window.dispatchEvent(event);
    }
    
    /**
     * 获取性能优化建议
     * @param {string} metric - 指标名称
     * @param {string} type - 警报类型
     * @param {object} data - 相关数据
     * @returns {array} 建议列表
     */
    getRecommendations(metric, type, data) {
        const recommendations = {
            pageLoadTime: [
                '优化图片大小和格式',
                '启用浏览器缓存',
                '压缩CSS和JavaScript文件',
                '使用CDN加速资源加载'
            ],
            firstContentfulPaint: [
                '减少关键渲染路径',
                '内联关键CSS',
                '延迟加载非关键资源',
                '优化字体加载'
            ],
            largestContentfulPaint: [
                '优化最大内容元素',
                '预加载重要资源',
                '减少服务器响应时间',
                '移除未使用的CSS'
            ],
            cumulativeLayoutShift: [
                '为图片和视频设置尺寸属性',
                '避免在现有内容上方插入内容',
                '使用transform动画而非改变布局的动画',
                '预留广告位空间'
            ],
            firstInputDelay: [
                '减少JavaScript执行时间',
                '分割长任务',
                '使用Web Workers处理计算密集型任务',
                '延迟加载第三方脚本'
            ],
            high_memory_usage: [
                '检查内存泄漏',
                '清理未使用的事件监听器',
                '优化图片和媒体资源',
                '减少DOM节点数量'
            ]
        };
        
        return recommendations[metric] || ['联系技术支持获取优化建议'];
    }
    
    /**
     * 测量函数执行时间
     * @param {string} name - 测量名称
     * @param {Function} fn - 要测量的函数
     * @returns {Promise} 函数执行结果
     */
    async measureFunction(name, fn) {
        const startTime = performance.now();
        
        try {
            const result = await fn();
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            this.addPerformanceData('function', {
                name: name,
                duration: duration,
                success: true,
                timestamp: Date.now()
            });
            
            if (window.logger) {
                window.logger.debug('函数执行时间', {
                    name: name,
                    duration: `${duration.toFixed(2)}ms`
                }, 'performance');
            }
            
            return result;
        } catch (error) {
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            this.addPerformanceData('function', {
                name: name,
                duration: duration,
                success: false,
                error: error.message,
                timestamp: Date.now()
            });
            
            throw error;
        }
    }
    
    /**
     * 开始性能标记
     * @param {string} name - 标记名称
     */
    mark(name) {
        if (performance.mark) {
            performance.mark(name);
        }
        
        if (window.logger) {
            window.logger.debug('性能标记', { name: name }, 'performance');
        }
    }
    
    /**
     * 测量两个标记之间的时间
     * @param {string} name - 测量名称
     * @param {string} startMark - 开始标记
     * @param {string} endMark - 结束标记
     */
    measure(name, startMark, endMark) {
        if (performance.measure) {
            performance.measure(name, startMark, endMark);
            
            const entries = performance.getEntriesByName(name, 'measure');
            if (entries.length > 0) {
                const duration = entries[entries.length - 1].duration;
                
                this.addPerformanceData('measure', {
                    name: name,
                    duration: duration,
                    startMark: startMark,
                    endMark: endMark,
                    timestamp: Date.now()
                });
                
                if (window.logger) {
                    window.logger.debug('性能测量', {
                        name: name,
                        duration: `${duration.toFixed(2)}ms`
                    }, 'performance');
                }
            }
        }
    }
    
    /**
     * 获取性能报告
     * @returns {object} 性能报告
     */
    getReport() {
        return {
            timestamp: new Date().toISOString(),
            metrics: this.metrics,
            thresholds: this.thresholds,
            dataPoints: this.performanceData.length,
            monitoring: this.isMonitoring,
            summary: this.generateSummary(),
            recommendations: this.generateOverallRecommendations()
        };
    }
    
    /**
     * 生成性能摘要
     * @returns {object} 性能摘要
     */
    generateSummary() {
        const summary = {
            pageLoad: {
                status: 'unknown',
                score: 0
            },
            userExperience: {
                status: 'unknown',
                score: 0
            },
            resourceUsage: {
                status: 'unknown',
                score: 0
            }
        };
        
        // 页面加载评分
        if (this.metrics.pageLoad.totalLoadTime) {
            const loadTime = this.metrics.pageLoad.totalLoadTime;
            if (loadTime < this.thresholds.pageLoadTime * 0.5) {
                summary.pageLoad = { status: 'excellent', score: 90 };
            } else if (loadTime < this.thresholds.pageLoadTime) {
                summary.pageLoad = { status: 'good', score: 70 };
            } else if (loadTime < this.thresholds.pageLoadTime * 1.5) {
                summary.pageLoad = { status: 'fair', score: 50 };
            } else {
                summary.pageLoad = { status: 'poor', score: 30 };
            }
        }
        
        // 用户体验评分
        const fcp = this.metrics.rendering['first-contentful-paint'];
        const lcp = this.metrics.rendering.largestContentfulPaint;
        const cls = this.metrics.rendering.cumulativeLayoutShift;
        const fid = this.metrics.userInteraction.firstInputDelay;
        
        let uxScore = 0;
        let uxChecks = 0;
        
        if (fcp) {
            uxScore += fcp < this.thresholds.firstContentfulPaint ? 25 : 10;
            uxChecks++;
        }
        if (lcp) {
            uxScore += lcp < this.thresholds.largestContentfulPaint ? 25 : 10;
            uxChecks++;
        }
        if (cls !== undefined) {
            uxScore += cls < this.thresholds.cumulativeLayoutShift ? 25 : 10;
            uxChecks++;
        }
        if (fid) {
            uxScore += fid < this.thresholds.firstInputDelay ? 25 : 10;
            uxChecks++;
        }
        
        if (uxChecks > 0) {
            const avgScore = uxScore / uxChecks;
            summary.userExperience = {
                status: avgScore > 20 ? 'good' : avgScore > 15 ? 'fair' : 'poor',
                score: Math.round(avgScore * 4) // 转换为100分制
            };
        }
        
        // 资源使用评分
        if (this.metrics.memory.usedJSHeapSize) {
            const memoryUsage = this.metrics.memory.usedJSHeapSize;
            if (memoryUsage < this.thresholds.memoryUsage * 0.5) {
                summary.resourceUsage = { status: 'excellent', score: 90 };
            } else if (memoryUsage < this.thresholds.memoryUsage) {
                summary.resourceUsage = { status: 'good', score: 70 };
            } else if (memoryUsage < this.thresholds.memoryUsage * 1.5) {
                summary.resourceUsage = { status: 'fair', score: 50 };
            } else {
                summary.resourceUsage = { status: 'poor', score: 30 };
            }
        }
        
        return summary;
    }
    
    /**
     * 生成总体优化建议
     * @returns {array} 建议列表
     */
    generateOverallRecommendations() {
        const recommendations = [];
        const summary = this.generateSummary();
        
        if (summary.pageLoad.score < 70) {
            recommendations.push('优化页面加载性能');
        }
        
        if (summary.userExperience.score < 70) {
            recommendations.push('改善用户体验指标');
        }
        
        if (summary.resourceUsage.score < 70) {
            recommendations.push('优化资源使用效率');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('性能表现良好，继续保持');
        }
        
        return recommendations;
    }
    
    /**
     * 清除性能数据
     */
    clearData() {
        this.performanceData = [];
        this.metrics = {
            pageLoad: {},
            userInteraction: {},
            memory: {},
            network: {},
            rendering: {}
        };
        
        if (window.logger) {
            window.logger.info('性能数据已清除', {}, 'performance');
        }
    }
}

// 创建全局性能监控实例
window.PerformanceMonitor = PerformanceMonitor;
window.performanceMonitor = new PerformanceMonitor();

// 暴露到全局作用域
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceMonitor;
}
